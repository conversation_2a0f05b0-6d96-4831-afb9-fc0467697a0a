"use client"

import { useState, useEffect } from "react"
import { useLanguage } from "../../contexts/LanguageContext"
import { Card, CardContent, CardHeader, CardTitle } from "../../components/feed-components/card"
import Button from "../../components/Button"
import {
  Download,
  BarChart3,
  TrendingUp,
  Package,
  Pill,
  Bird,
  FileText,
  RefreshCw,
  Calendar,
  Printer,
  FileSpreadsheet,
  Database,
  Settings,
  AlertCircle,
  CheckCircle,
  XCircle,
  Globe,
  TestTube,
  ChevronDown,
} from "lucide-react"
import {
  BarChart,
  Bar,
  Line,
  Area,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
} from "recharts"

const ReportsPage = () => {
  const { language, translations } = useLanguage()
  const [reportType, setReportType] = useState("chickens")
  const [timeRange, setTimeRange] = useState("monthly")
  const [reportMethod, setReportMethod] = useState("summary")
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")
  const [reportData, setReportData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [apiStatus, setApiStatus] = useState({})
  const [testingEndpoints, setTestingEndpoints] = useState(false)
  const [useMockData, setUseMockData] = useState(false)
  const [customEndpoint, setCustomEndpoint] = useState("http://localhost:3000/admin/chickens/reports")
  const [showExportMenu, setShowExportMenu] = useState(false)

  const t = (key) => {
    if (!translations || !translations[language]) return key
    return translations[language][key] || key
  }

  useEffect(() => {
    setDefaultDates()
  }, [])

  // Close export menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showExportMenu && !event.target.closest(".export-menu-container")) {
        setShowExportMenu(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [showExportMenu])

  const setDefaultDates = () => {
    const today = new Date()
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)

    setStartDate(firstDayOfMonth.toISOString().split("T")[0])
    setEndDate(today.toISOString().split("T")[0])
  }

  const getAuthHeaders = () => {
    const token =
      localStorage.getItem("token") ||
      localStorage.getItem("authToken") ||
      localStorage.getItem("accessToken") ||
      localStorage.getItem("jwt") ||
      localStorage.getItem("access_token")

    return {
      "Content-Type": "application/json",
      Accept: "application/json",
      ...(token && { Authorization: `Bearer ${token}` }),
    }
  }

  const testApiEndpoints = async () => {
    setTestingEndpoints(true)
    setError(null)

    const endpoints = [
      `http://localhost:3000/admin/${reportType}/reports`,
      `http://localhost:3000/api/admin/${reportType}/reports`,
      `http://localhost:3000/api/v1/admin/${reportType}/reports`,
      `http://localhost:3000/${reportType}/reports`,
      `http://localhost:3000/api/${reportType}/reports`,
      `http://localhost:3000/api/v1/${reportType}/reports`,
      `http://localhost:3000/reports/${reportType}`,
      `http://localhost:3000/api/reports/${reportType}`,
      customEndpoint.replace("chickens", reportType),
    ]

    const status = {}
    const headers = getAuthHeaders()

    for (const endpoint of endpoints) {
      try {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 5000)

        const response = await fetch(endpoint, {
          headers,
          method: "GET",
          signal: controller.signal,
        })

        clearTimeout(timeoutId)

        status[endpoint] = {
          status: response.status,
          ok: response.ok,
          statusText: response.statusText,
          contentType: response.headers.get("content-type"),
        }

        if (response.ok) {
          try {
            const text = await response.text()
            if (text.trim().startsWith("{") || text.trim().startsWith("[")) {
              const data = JSON.parse(text)
              status[endpoint].hasData = true
              status[endpoint].dataPreview = JSON.stringify(data).substring(0, 100) + "..."
            } else {
              status[endpoint].hasData = false
              status[endpoint].dataPreview = text.substring(0, 100) + "..."
            }
          } catch (e) {
            status[endpoint].hasData = false
            status[endpoint].error = "Invalid JSON response"
          }
        }
      } catch (error) {
        status[endpoint] = {
          status: 0,
          ok: false,
          error: error.name === "AbortError" ? "Timeout" : error.message,
        }
      }
    }

    setApiStatus(status)
    setTestingEndpoints(false)

    const workingEndpoints = Object.entries(status).filter(([_, info]) => info.ok && info.hasData)
    if (workingEndpoints.length === 0) {
      setError("No working API endpoints found. You can use mock data for testing or check your server configuration.")
    }
  }

  const generateMockData = () => {
    const mockTransactions = []
    const startDateObj = new Date(startDate)
    const endDateObj = new Date(endDate)
    const daysDiff = Math.ceil((endDateObj - startDateObj) / (1000 * 60 * 60 * 24))

    for (let i = 0; i < Math.min(daysDiff * 2, 50); i++) {
      const randomDate = new Date(startDateObj.getTime() + Math.random() * (endDateObj - startDateObj))
      const types =
        reportType === "chickens"
          ? ["purchase", "allocation", "buyback", "distribution"]
          : ["purchase", "transfer", "distribution"]

      mockTransactions.push({
        id: i + 1,
        type: types[Math.floor(Math.random() * types.length)],
        date: randomDate.toISOString().split("T")[0],
        amount: Math.floor(Math.random() * 1000) + 100,
        quantity: Math.floor(Math.random() * 100) + 10,
        description: `Mock ${reportType} transaction ${i + 1}`,
        created_at: randomDate.toISOString(),
      })
    }

    return {
      success: true,
      data: {
        transactions: mockTransactions,
        [`total${reportType.charAt(0).toUpperCase() + reportType.slice(1)}`]: mockTransactions.length,
        totalRevenue: mockTransactions.reduce((sum, t) => sum + t.amount, 0),
        totalTransactions: mockTransactions.length,
      },
    }
  }

  const generateReportData = async () => {
    if (!startDate || !endDate) {
      throw new Error("Please select both start and end dates")
    }

    let result

    if (useMockData) {
      result = generateMockData()
    } else {
      const headers = getAuthHeaders()
      const workingEndpoint = Object.entries(apiStatus).find(([_, info]) => info.ok && info.hasData)?.[0]

      if (!workingEndpoint) {
        throw new Error("No working API endpoint available. Please test endpoints first or use mock data.")
      }

      const queryParams = new URLSearchParams({
        startDate: startDate,
        endDate: endDate,
        timeRange: timeRange,
        method: reportMethod,
      })

      const response = await fetch(`${workingEndpoint}?${queryParams}`, {
        headers,
        method: "GET",
      })

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`)
      }

      const responseText = await response.text()

      if (responseText.trim().startsWith("<!DOCTYPE") || responseText.trim().startsWith("<html")) {
        throw new Error("Server returned HTML instead of JSON. Check authentication or endpoint configuration.")
      }

      try {
        result = JSON.parse(responseText)
      } catch (parseError) {
        throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}...`)
      }
    }

    let reportDataToProcess = null

    if (result.success && result.data) {
      reportDataToProcess = result.data
    } else if (result.data) {
      reportDataToProcess = result.data
    } else if (Array.isArray(result)) {
      reportDataToProcess = { transactions: result }
    } else {
      reportDataToProcess = result
    }

    const processedData = processReportData(reportDataToProcess, timeRange, reportMethod)
    const summary = calculateSummary(reportDataToProcess)

    return {
      type: reportType,
      timeRange,
      method: reportMethod,
      startDate,
      endDate,
      rawData: reportDataToProcess,
      processed: processedData,
      summary: summary,
      metadata: result.metadata || {},
      generatedAt: new Date().toISOString(),
      isMockData: useMockData,
    }
  }

  const processReportData = (data, range, method) => {
    const processed = {}
    let transactions = []

    if (data.transactions && Array.isArray(data.transactions)) {
      transactions = data.transactions
    } else if (reportType === "chickens") {
      transactions = [
        ...(data.purchases || []),
        ...(data.allocations || []),
        ...(data.buybacks || []),
        ...(data.distributions || []),
      ]
    } else if (reportType === "drugs") {
      transactions = [...(data.purchases || []), ...(data.transfers || []), ...(data.distributions || [])]
    } else if (reportType === "seeds") {
      transactions = [...(data.purchases || []), ...(data.transfers || []), ...(data.distributions || [])]
    } else if (Array.isArray(data)) {
      transactions = data
    }

    transactions.forEach((transaction) => {
      const dateFields = [
        "date",
        "created_at",
        "purchase_date",
        "transfer_date",
        "allocation_date",
        "distribution_date",
        "buyback_date",
        "createdAt",
      ]

      let transactionDate = null
      for (const field of dateFields) {
        if (transaction[field]) {
          transactionDate = new Date(transaction[field])
          break
        }
      }

      if (!transactionDate || isNaN(transactionDate.getTime())) {
        transactionDate = new Date()
      }

      let key = ""
      switch (range) {
        case "daily":
          key = transactionDate.toISOString().split("T")[0]
          break
        case "monthly":
          key = `${transactionDate.getFullYear()}-${String(transactionDate.getMonth() + 1).padStart(2, "0")}`
          break
        case "yearly":
          key = transactionDate.getFullYear().toString()
          break
      }

      if (!processed[key]) {
        processed[key] = {
          transactions: 0,
          totalAmount: 0,
          totalQuantity: 0,
          purchases: 0,
          transfers: 0,
          distributions: 0,
          allocations: 0,
          buybacks: 0,
          details: method === "detailed" ? [] : undefined,
        }
      }

      processed[key].transactions += 1

      const amountFields = ["amount", "total_amount", "totalAmount", "price", "cost"]
      let amount = 0
      for (const field of amountFields) {
        if (transaction[field] && !isNaN(Number(transaction[field]))) {
          amount = Number(transaction[field])
          break
        }
      }
      processed[key].totalAmount += amount

      const quantityFields = ["quantity", "qty", "count", "number"]
      let quantity = 0
      for (const field of quantityFields) {
        if (transaction[field] && !isNaN(Number(transaction[field]))) {
          quantity = Number(transaction[field])
          break
        }
      }
      processed[key].totalQuantity += quantity

      if (method === "detailed") {
        processed[key].details.push(transaction)
      }

      const type = transaction.type || transaction.transaction_type || ""
      if (type.includes("purchase") || transaction.purchase_date) {
        processed[key].purchases += 1
      } else if (type.includes("transfer") || transaction.transfer_date) {
        processed[key].transfers += 1
      } else if (type.includes("distribution") || transaction.distribution_date) {
        processed[key].distributions += 1
      } else if (type.includes("allocation") || transaction.allocation_date) {
        processed[key].allocations += 1
      } else if (type.includes("buyback") || transaction.buyback_date) {
        processed[key].buybacks += 1
      }
    })

    return processed
  }

  const calculateSummary = (data) => {
    let totalTransactions = 0
    let totalAmount = 0
    let totalQuantity = 0
    let totalPurchases = 0
    let totalTransfers = 0

    let allTransactions = []

    if (data.transactions && Array.isArray(data.transactions)) {
      allTransactions = data.transactions
    } else if (Array.isArray(data)) {
      allTransactions = data
    } else {
      Object.values(data).forEach((category) => {
        if (Array.isArray(category)) {
          allTransactions = [...allTransactions, ...category]
        }
      })
    }

    allTransactions.forEach((transaction) => {
      totalTransactions += 1

      const amountFields = ["amount", "total_amount", "totalAmount", "price", "cost"]
      for (const field of amountFields) {
        if (transaction[field] && !isNaN(Number(transaction[field]))) {
          totalAmount += Number(transaction[field])
          break
        }
      }

      const quantityFields = ["quantity", "qty", "count", "number"]
      for (const field of quantityFields) {
        if (transaction[field] && !isNaN(Number(transaction[field]))) {
          totalQuantity += Number(transaction[field])
          break
        }
      }

      const type = transaction.type || transaction.transaction_type || ""
      if (type.includes("purchase") || transaction.purchase_date) {
        totalPurchases += 1
      } else if (type.includes("transfer") || transaction.transfer_date) {
        totalTransfers += 1
      }
    })

    return {
      totalTransactions,
      totalAmount,
      totalQuantity,
      totalPurchases,
      totalTransfers,
      averageTransactionValue: totalTransactions > 0 ? totalAmount / totalTransactions : 0,
    }
  }

  const handleExport = async (format) => {
    setLoading(true)
    setError(null)
    setShowExportMenu(false)

    try {
      const data = await generateReportData()
      setReportData(data)

      // Generate the export based on format
      switch (format) {
        case "csv":
          await exportToCSV(data)
          break
        case "json":
          await exportToJSON(data)
          break
        case "excel":
          await exportToExcel(data)
          break
        case "pdf":
          await exportToPDF(data)
          break
        case "print":
          await handlePrint(data)
          break
        default:
          throw new Error("Unsupported export format")
      }
    } catch (error) {
      console.error(`Error generating ${format} report:`, error)
      setError(error.message)
    } finally {
      setLoading(false)
    }
  }

  const exportToCSV = async (data) => {
    let csv = `${reportType.toUpperCase()} Report - ${reportMethod.toUpperCase()} - ${timeRange.toUpperCase()}\n`
    csv += `Period: ${startDate} to ${endDate}\n`
    csv += `Generated: ${new Date().toLocaleString()}\n`
    csv += `Data Source: ${data.isMockData ? "Mock Data" : "API Data"}\n\n`

    // Summary section
    csv += "SUMMARY\n"
    csv += `Total Transactions,${data.summary.totalTransactions}\n`
    csv += `Total Amount,$${data.summary.totalAmount.toFixed(2)}\n`
    csv += `Total Quantity,${data.summary.totalQuantity}\n`
    csv += `Average Transaction Value,$${data.summary.averageTransactionValue.toFixed(2)}\n\n`

    // Detailed breakdown
    csv += "DETAILED BREAKDOWN\n"
    const periodLabel = timeRange === "daily" ? "Date" : timeRange === "monthly" ? "Month" : "Year"
    csv += `${periodLabel},Transactions,Amount ($),Quantity,Purchases,Transfers`

    if (reportType === "chickens") {
      csv += ",Allocations,Buybacks,Distributions"
    } else {
      csv += ",Distributions"
    }
    csv += "\n"

    Object.entries(data.processed)
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([period, periodData]) => {
        csv += `${period},${periodData.transactions},${periodData.totalAmount.toFixed(2)},${periodData.totalQuantity},${periodData.purchases},${periodData.transfers}`

        if (reportType === "chickens") {
          csv += `,${periodData.allocations},${periodData.buybacks},${periodData.distributions}`
        } else {
          csv += `,${periodData.distributions}`
        }
        csv += "\n"
      })

    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" })
    downloadFile(blob, `${reportType}_report_${reportMethod}_${timeRange}_${startDate}_to_${endDate}.csv`)
  }

  const exportToJSON = async (data) => {
    const exportData = {
      ...data,
      exportedAt: new Date().toISOString(),
      exportFormat: "json",
    }

    const jsonContent = JSON.stringify(exportData, null, 2)
    const blob = new Blob([jsonContent], { type: "application/json;charset=utf-8;" })
    downloadFile(blob, `${reportType}_report_${reportMethod}_${timeRange}_${startDate}_to_${endDate}.json`)
  }

  const exportToExcel = async (data) => {
    let excel = `${reportType.toUpperCase()} Report\t${reportMethod.toUpperCase()}\t${timeRange.toUpperCase()}\n`
    excel += `Period:\t${startDate}\tto\t${endDate}\n`
    excel += `Generated:\t${new Date().toLocaleString()}\n\n`

    // Summary section
    excel += "SUMMARY\n"
    excel += `Total Transactions\t${data.summary.totalTransactions}\n`
    excel += `Total Amount\t${data.summary.totalAmount.toFixed(2)}\n`
    excel += `Total Quantity\t${data.summary.totalQuantity}\n`
    excel += `Average Transaction Value\t${data.summary.averageTransactionValue.toFixed(2)}\n\n`

    // Detailed breakdown
    excel += "DETAILED BREAKDOWN\n"
    const periodLabel = timeRange === "daily" ? "Date" : timeRange === "monthly" ? "Month" : "Year"
    excel += `${periodLabel}\tTransactions\tAmount\tQuantity\tPurchases\tTransfers`

    if (reportType === "chickens") {
      excel += "\tAllocations\tBuybacks\tDistributions"
    } else {
      excel += "\tDistributions"
    }
    excel += "\n"

    Object.entries(data.processed)
      .sort(([a], [b]) => a.localeCompare(b))
      .forEach(([period, periodData]) => {
        excel += `${period}\t${periodData.transactions}\t${periodData.totalAmount.toFixed(2)}\t${periodData.totalQuantity}\t${periodData.purchases}\t${periodData.transfers}`

        if (reportType === "chickens") {
          excel += `\t${periodData.allocations}\t${periodData.buybacks}\t${periodData.distributions}`
        } else {
          excel += `\t${periodData.distributions}`
        }
        excel += "\n"
      })

    const blob = new Blob([excel], { type: "application/vnd.ms-excel;charset=utf-8;" })
    downloadFile(blob, `${reportType}_report_${reportMethod}_${timeRange}_${startDate}_to_${endDate}.xls`)
  }

  const exportToPDF = async (data) => {
    const printContent = generatePrintableHTML(data)
    const printWindow = window.open("", "_blank")
    printWindow.document.write(printContent)
    printWindow.document.close()

    printWindow.onload = () => {
      printWindow.print()
      setTimeout(() => {
        printWindow.close()
      }, 1000)
    }
  }

  const handlePrint = async (data) => {
    const printContent = generatePrintableHTML(data)
    const printWindow = window.open("", "_blank")
    printWindow.document.write(printContent)
    printWindow.document.close()
    printWindow.onload = () => {
      printWindow.print()
      printWindow.close()
    }
  }

  const generatePrintableHTML = (data) => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${reportType.toUpperCase()} Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .summary { margin-bottom: 30px; }
          .summary-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin: 20px 0; }
          .summary-card { border: 1px solid #ddd; padding: 15px; text-align: center; }
          table { width: 100%; border-collapse: collapse; margin: 20px 0; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f5f5f5; }
          .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          .mock-data { color: #ff6b35; font-weight: bold; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${reportType.toUpperCase()} Report</h1>
          <h2>${reportMethod.toUpperCase()} - ${timeRange.toUpperCase()}</h2>
          <p>Period: ${startDate} to ${endDate}</p>
          <p>Generated: ${new Date().toLocaleString()}</p>
          ${data.isMockData ? '<p class="mock-data">⚠️ Using Mock Data for Testing</p>' : ""}
        </div>

        <div class="summary">
          <h3>Summary</h3>
          <div class="summary-grid">
            <div class="summary-card">
              <h4>${data.summary.totalTransactions}</h4>
              <p>Total Transactions</p>
            </div>
            <div class="summary-card">
              <h4>$${data.summary.totalAmount.toLocaleString()}</h4>
              <p>Total Amount</p>
            </div>
            <div class="summary-card">
              <h4>${data.summary.totalQuantity.toLocaleString()}</h4>
              <p>Total Quantity</p>
            </div>
            <div class="summary-card">
              <h4>$${data.summary.averageTransactionValue.toFixed(2)}</h4>
              <p>Avg Transaction</p>
            </div>
          </div>
        </div>

        <div class="breakdown">
          <h3>Detailed Breakdown</h3>
          <table>
            <thead>
              <tr>
                <th>${timeRange === "daily" ? "Date" : timeRange === "monthly" ? "Month" : "Year"}</th>
                <th>Transactions</th>
                <th>Amount ($)</th>
                <th>Quantity</th>
                <th>Purchases</th>
                <th>Transfers</th>
                ${reportType === "chickens" ? "<th>Allocations</th><th>Buybacks</th>" : ""}
                <th>Distributions</th>
              </tr>
            </thead>
            <tbody>
              ${Object.entries(data.processed)
                .sort(([a], [b]) => a.localeCompare(b))
                .map(
                  ([period, periodData]) => `
                <tr>
                  <td>${period}</td>
                  <td>${periodData.transactions}</td>
                  <td>$${periodData.totalAmount.toLocaleString()}</td>
                  <td>${periodData.totalQuantity.toLocaleString()}</td>
                  <td>${periodData.purchases}</td>
                  <td>${periodData.transfers}</td>
                  ${reportType === "chickens" ? `<td>${periodData.allocations}</td><td>${periodData.buybacks}</td>` : ""}
                  <td>${periodData.distributions}</td>
                </tr>
              `,
                )
                .join("")}
            </tbody>
          </table>
        </div>

        <div class="footer">
          <p>Report generated by Management System on ${new Date().toLocaleString()}</p>
          <p>Data Source: ${data.isMockData ? "Mock Data (for testing purposes)" : "Live API Data"}</p>
        </div>
      </body>
      </html>
    `
  }

  const downloadFile = (blob, filename) => {
    const link = document.createElement("a")
    const url = URL.createObjectURL(blob)
    link.setAttribute("href", url)
    link.setAttribute("download", filename)
    link.style.visibility = "hidden"
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // Chart Components
  const TransactionTrendsChart = ({ data, timeRange }) => {
    const chartData = Object.entries(data.processed)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([period, periodData]) => ({
        period: period,
        transactions: periodData.transactions,
        amount: periodData.totalAmount,
        quantity: periodData.totalQuantity,
      }))

    return (
      <ResponsiveContainer width="100%" height={400}>
        <ComposedChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="period"
            tick={{ fontSize: 12 }}
            angle={timeRange === "daily" ? -45 : 0}
            textAnchor={timeRange === "daily" ? "end" : "middle"}
            height={timeRange === "daily" ? 60 : 30}
          />
          <YAxis yAxisId="left" orientation="left" stroke="#FF6B00" />
          <YAxis yAxisId="right" orientation="right" stroke="#2C3E50" />
          <Tooltip
            contentStyle={{
              backgroundColor: "white",
              border: "1px solid #ccc",
              borderRadius: "8px",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
            }}
            formatter={(value, name) => [
              name === "amount" ? `$${value.toLocaleString()}` : value.toLocaleString(),
              name === "amount" ? "Amount" : name === "transactions" ? "Transactions" : "Quantity",
            ]}
          />
          <Legend />
          <Bar yAxisId="left" dataKey="transactions" fill="#FF6B00" name="Transactions" radius={[4, 4, 0, 0]} />
          <Line yAxisId="right" type="monotone" dataKey="amount" stroke="#2C3E50" strokeWidth={3} name="Amount ($)" />
          <Area
            yAxisId="right"
            type="monotone"
            dataKey="quantity"
            fill="#388E3C"
            fillOpacity={0.3}
            stroke="#388E3C"
            name="Quantity"
          />
        </ComposedChart>
      </ResponsiveContainer>
    )
  }

  const TransactionTypesChart = ({ data, reportType }) => {
    const typeData = Object.entries(data.processed).reduce(
      (acc, [_, periodData]) => {
        acc.purchases += periodData.purchases
        acc.transfers += periodData.transfers
        acc.distributions += periodData.distributions
        if (reportType === "chickens") {
          acc.allocations += periodData.allocations
          acc.buybacks += periodData.buybacks
        }
        return acc
      },
      { purchases: 0, transfers: 0, distributions: 0, allocations: 0, buybacks: 0 },
    )

    const chartData = [
      { name: "Purchases", value: typeData.purchases, color: "#FF6B00" },
      { name: "Transfers", value: typeData.transfers, color: "#2C3E50" },
      { name: "Distributions", value: typeData.distributions, color: "#388E3C" },
    ]

    if (reportType === "chickens") {
      chartData.push(
        { name: "Allocations", value: typeData.allocations, color: "#9C27B0" },
        { name: "Buybacks", value: typeData.buybacks, color: "#D32F2F" },
      )
    }

    const filteredData = chartData.filter((item) => item.value > 0)

    return (
      <ResponsiveContainer width="100%" height={400}>
        <PieChart>
          <Pie
            data={filteredData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(1)}%`}
            outerRadius={120}
            fill="#8884d8"
            dataKey="value"
          >
            {filteredData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip
            contentStyle={{
              backgroundColor: "white",
              border: "1px solid #ccc",
              borderRadius: "8px",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
            }}
            formatter={(value) => [value.toLocaleString(), "Count"]}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    )
  }

  const AmountDistributionChart = ({ data }) => {
    const chartData = Object.entries(data.processed)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([period, periodData]) => ({
        period: period,
        purchases: periodData.purchases * (periodData.totalAmount / periodData.transactions || 0),
        transfers: periodData.transfers * (periodData.totalAmount / periodData.transactions || 0),
        distributions: periodData.distributions * (periodData.totalAmount / periodData.transactions || 0),
        allocations: periodData.allocations * (periodData.totalAmount / periodData.transactions || 0),
        buybacks: periodData.buybacks * (periodData.totalAmount / periodData.transactions || 0),
      }))

    return (
      <ResponsiveContainer width="100%" height={400}>
        <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis dataKey="period" tick={{ fontSize: 12 }} />
          <YAxis tickFormatter={(value) => `$${value.toLocaleString()}`} />
          <Tooltip
            contentStyle={{
              backgroundColor: "white",
              border: "1px solid #ccc",
              borderRadius: "8px",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
            }}
            formatter={(value) => [`$${value.toLocaleString()}`, "Amount"]}
          />
          <Legend />
          <Bar dataKey="purchases" stackId="a" fill="#FF6B00" name="Purchases" />
          <Bar dataKey="transfers" stackId="a" fill="#2C3E50" name="Transfers" />
          <Bar dataKey="distributions" stackId="a" fill="#388E3C" name="Distributions" />
          <Bar dataKey="allocations" stackId="a" fill="#9C27B0" name="Allocations" />
          <Bar dataKey="buybacks" stackId="a" fill="#D32F2F" name="Buybacks" />
        </BarChart>
      </ResponsiveContainer>
    )
  }

  const QuantityTrendsChart = ({ data, timeRange }) => {
    const chartData = Object.entries(data.processed)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([period, periodData]) => ({
        period: period,
        quantity: periodData.totalQuantity,
        transactions: periodData.transactions,
        avgQuantityPerTransaction: periodData.transactions > 0 ? periodData.totalQuantity / periodData.transactions : 0,
      }))

    return (
      <ResponsiveContainer width="100%" height={400}>
        <ComposedChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis
            dataKey="period"
            tick={{ fontSize: 12 }}
            angle={timeRange === "daily" ? -45 : 0}
            textAnchor={timeRange === "daily" ? "end" : "middle"}
            height={timeRange === "daily" ? 60 : 30}
          />
          <YAxis yAxisId="left" orientation="left" stroke="#388E3C" />
          <YAxis yAxisId="right" orientation="right" stroke="#FF6B00" />
          <Tooltip
            contentStyle={{
              backgroundColor: "white",
              border: "1px solid #ccc",
              borderRadius: "8px",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
            }}
            formatter={(value, name) => [
              value.toLocaleString(),
              name === "quantity" ? "Total Quantity" : name === "transactions" ? "Transactions" : "Avg Qty/Transaction",
            ]}
          />
          <Legend />
          <Area
            yAxisId="left"
            type="monotone"
            dataKey="quantity"
            fill="#388E3C"
            fillOpacity={0.3}
            stroke="#388E3C"
            name="Total Quantity"
          />
          <Bar yAxisId="right" dataKey="transactions" fill="#FF6B00" name="Transactions" />
          <Line
            yAxisId="left"
            type="monotone"
            dataKey="avgQuantityPerTransaction"
            stroke="#2C3E50"
            strokeWidth={2}
            name="Avg Qty/Transaction"
          />
        </ComposedChart>
      </ResponsiveContainer>
    )
  }

  const SummaryMetricsChart = ({ data }) => {
    const metricsData = [
      {
        name: "Total Transactions",
        value: data.summary.totalTransactions,
        color: "#FF6B00",
        icon: "📊",
      },
      {
        name: "Total Amount",
        value: data.summary.totalAmount,
        color: "#388E3C",
        icon: "💰",
        format: "currency",
      },
      {
        name: "Total Quantity",
        value: data.summary.totalQuantity,
        color: "#2C3E50",
        icon: "📦",
      },
      {
        name: "Avg Transaction",
        value: data.summary.averageTransactionValue,
        color: "#9C27B0",
        icon: "📈",
        format: "currency",
      },
    ]

    return (
      <ResponsiveContainer width="100%" height={300}>
        <BarChart data={metricsData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis dataKey="name" tick={{ fontSize: 12 }} />
          <YAxis tickFormatter={(value) => value.toLocaleString()} />
          <Tooltip
            contentStyle={{
              backgroundColor: "white",
              border: "1px solid #ccc",
              borderRadius: "8px",
              boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
            }}
            formatter={(value, name, props) => [
              props.payload.format === "currency" ? `$${value.toLocaleString()}` : value.toLocaleString(),
              name,
            ]}
          />
          <Bar dataKey="value" fill="#FF6B00" radius={[4, 4, 0, 0]}>
            {metricsData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    )
  }

  const reportTypeOptions = [
    {
      value: "chickens",
      label: "Chickens",
      icon: Bird,
      color: "text-green-600",
      bgColor: "bg-green-50",
      description: "Purchases, allocations, buybacks, distributions",
    },
    {
      value: "drugs",
      label: "Drugs",
      icon: Pill,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      description: "Purchases, transfers, distributions",
    },
    {
      value: "seeds",
      label: "Seeds",
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      description: "Purchases, transfers, distributions",
    },
  ]

  const timeRangeOptions = [
    { value: "daily", label: "Daily", icon: Calendar },
    { value: "monthly", label: "Monthly", icon: Calendar },
    { value: "yearly", label: "Yearly", icon: Calendar },
  ]

  const reportMethodOptions = [
    {
      value: "summary",
      label: "Summary Report",
      icon: BarChart3,
      description: "High-level overview with key metrics",
    },
    {
      value: "detailed",
      label: "Detailed Report",
      icon: FileText,
      description: "Complete transaction details and breakdowns",
    },
    {
      value: "analytics",
      label: "Analytics Report",
      icon: TrendingUp,
      description: "Advanced analytics with trends and insights",
    },
  ]

  const exportOptions = [
    {
      label: "Export as PDF",
      icon: <FileText className="h-4 w-4" />,
      action: () => handleExport("pdf"),
      description: "Formatted PDF document",
    },
    {
      label: "Export as CSV",
      icon: <FileSpreadsheet className="h-4 w-4" />,
      action: () => handleExport("csv"),
      description: "Comma-separated values",
    },
    {
      label: "Export as Excel",
      icon: <FileSpreadsheet className="h-4 w-4" />,
      action: () => handleExport("excel"),
      description: "Microsoft Excel format",
    },
    {
      label: "Export as JSON",
      icon: <Database className="h-4 w-4" />,
      action: () => handleExport("json"),
      description: "JSON data format",
    },
    {
      label: "Print Report",
      icon: <Printer className="h-4 w-4" />,
      action: () => handleExport("print"),
      description: "Print current page",
    },
  ]

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === "ps" ? "rtl" : "ltr"}`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-2">
          <div className="flex flex-col">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {t("reports") || "Management Reports"}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Generate comprehensive reports with multiple export options
              </p>
            </div>
          </div>
          <div className="flex justify-end items-right gap-2">
            <div className="export-menu-container relative">
              <button
                onClick={() => setShowExportMenu(!showExportMenu)}
                disabled={loading}
                className="flex items-center px-4 py-2 bg-[#FF6B00] text-white rounded-lg hover:bg-[#e55a00] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? <RefreshCw className="h-4 w-4 mr-2 animate-spin" /> : <Download className="h-4 w-4 mr-2" />}
                {loading ? "Generating..." : "Generate & Export Report"}
                <ChevronDown className="h-4 w-4 ml-2" />
              </button>

              {showExportMenu && (
                <div className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                  <div className="py-2">
                    {exportOptions.map((option, index) => (
                      <button
                        key={index}
                        onClick={option.action}
                        disabled={loading}
                        className="w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <div className="flex items-center text-gray-600 dark:text-gray-300 mr-3">{option.icon}</div>
                        <div>
                          <div className="font-medium text-gray-900 dark:text-white">{option.label}</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">{option.description}</div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-700">
                <AlertCircle className="h-5 w-5" />
                <div>
                  <h4 className="font-medium">Error</h4>
                  <p className="text-sm mt-1">{error}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* API Testing and Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              API Configuration & Testing
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Custom Endpoint Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Custom API Endpoint (Optional)
              </label>
              <input
                type="text"
                value={customEndpoint}
                onChange={(e) => setCustomEndpoint(e.target.value)}
                placeholder="http://localhost:3000/admin/chickens/reports"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
              />
            </div>

            {/* Mock Data Toggle */}
            <div className="flex items-center gap-3">
              <input
                type="checkbox"
                id="useMockData"
                checked={useMockData}
                onChange={(e) => setUseMockData(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="useMockData" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Use Mock Data for Testing (Skip API calls)
              </label>
            </div>

            {/* Test Endpoints Button */}
            <div className="flex gap-3">
              <Button
                onClick={testApiEndpoints}
                disabled={testingEndpoints || useMockData}
                variant="secondary"
                className="flex items-center gap-2"
              >
                {testingEndpoints ? <RefreshCw className="h-4 w-4 animate-spin" /> : <TestTube className="h-4 w-4" />}
                {testingEndpoints ? "Testing Endpoints..." : "Test API Endpoints"}
              </Button>
            </div>

            {/* API Status Display */}
            {Object.keys(apiStatus).length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API Endpoint Status:</h4>
                <div className="space-y-2 max-h-60 overflow-y-auto">
                  {Object.entries(apiStatus).map(([endpoint, status]) => (
                    <div
                      key={endpoint}
                      className={`p-3 rounded-lg border text-sm ${
                        status.ok && status.hasData
                          ? "border-green-200 bg-green-50 text-green-800"
                          : status.ok
                            ? "border-yellow-200 bg-yellow-50 text-yellow-800"
                            : "border-red-200 bg-red-50 text-red-800"
                      }`}
                    >
                      <div className="flex items-center gap-2 mb-1">
                        {status.ok && status.hasData ? (
                          <CheckCircle className="h-4 w-4" />
                        ) : status.ok ? (
                          <AlertCircle className="h-4 w-4" />
                        ) : (
                          <XCircle className="h-4 w-4" />
                        )}
                        <span className="font-medium">{endpoint}</span>
                      </div>
                      <div className="text-xs">
                        Status: {status.status} {status.statusText}
                        {status.error && ` - ${status.error}`}
                        {status.contentType && ` | Content-Type: ${status.contentType}`}
                        {status.dataPreview && (
                          <div className="mt-1 p-2 bg-white/50 rounded border">Preview: {status.dataPreview}</div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Report Configuration */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Report Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Report Type Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Select Report Category
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {reportTypeOptions.map((option) => {
                  const IconComponent = option.icon
                  return (
                    <button
                      key={option.value}
                      onClick={() => setReportType(option.value)}
                      className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                        reportType === option.value
                          ? `border-gray-400 ${option.bgColor} ring-2 ring-gray-200`
                          : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className={`p-2 rounded-lg ${option.bgColor}`}>
                          <IconComponent className={`h-5 w-5 ${option.color}`} />
                        </div>
                        <div>
                          <span className="font-medium text-gray-900 dark:text-white block">{option.label}</span>
                          <span className="text-xs text-gray-500 mt-1">{option.description}</span>
                        </div>
                      </div>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Report Method Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Select Report Method
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {reportMethodOptions.map((option) => {
                  const IconComponent = option.icon
                  return (
                    <button
                      key={option.value}
                      onClick={() => setReportMethod(option.value)}
                      className={`p-4 rounded-lg border-2 transition-all duration-200 text-left ${
                        reportMethod === option.value
                          ? "border-blue-500 bg-blue-50 ring-2 ring-blue-200"
                          : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="p-2 rounded-lg bg-blue-50">
                          <IconComponent className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <span className="font-medium text-gray-900 dark:text-white block">{option.label}</span>
                          <span className="text-xs text-gray-500 mt-1">{option.description}</span>
                        </div>
                      </div>
                    </button>
                  )
                })}
              </div>
            </div>

            {/* Time Range Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Time Period</label>
              <div className="flex flex-wrap gap-2">
                {timeRangeOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => setTimeRange(option.value)}
                    className={`px-4 py-2 rounded-lg border transition-all duration-200 flex items-center gap-2 ${
                      timeRange === option.value
                        ? "border-blue-500 bg-blue-50 text-blue-700"
                        : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                    }`}
                  >
                    <option.icon className="h-4 w-4" />
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Date Range Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Start Date</label>
                <input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">End Date</label>
                <input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Report Results */}
        {reportData && (
          <div className="space-y-6">
            {/* Report Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  {reportData.type.charAt(0).toUpperCase() + reportData.type.slice(1)} Report Summary
                  {reportData.isMockData && (
                    <span className="text-sm bg-orange-100 text-orange-800 px-2 py-1 rounded-full">Mock Data</span>
                  )}
                </CardTitle>
                <p className="text-sm text-gray-500">
                  {reportData.method.charAt(0).toUpperCase() + reportData.method.slice(1)}{" "}
                  {reportData.timeRange.charAt(0).toUpperCase() + reportData.timeRange.slice(1)} report from{" "}
                  {reportData.startDate} to {reportData.endDate}
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{reportData.summary.totalTransactions}</div>
                    <div className="text-sm text-blue-700">Total Transactions</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      ${reportData.summary.totalAmount.toLocaleString()}
                    </div>
                    <div className="text-sm text-green-700">Total Amount</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">
                      {reportData.summary.totalQuantity.toLocaleString()}
                    </div>
                    <div className="text-sm text-purple-700">Total Quantity</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">
                      ${reportData.summary.averageTransactionValue.toFixed(2)}
                    </div>
                    <div className="text-sm text-orange-700">Avg Transaction</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Interactive Charts Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Transaction Trends Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-blue-600" />
                    Transaction Trends Over Time
                  </CardTitle>
                  <p className="text-sm text-gray-500">Combined view of transactions, amounts, and quantities</p>
                </CardHeader>
                <CardContent>
                  <TransactionTrendsChart data={reportData} timeRange={reportData.timeRange} />
                </CardContent>
              </Card>

              {/* Transaction Types Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-purple-600" />
                    Transaction Types Distribution
                  </CardTitle>
                  <p className="text-sm text-gray-500">Breakdown of different transaction types</p>
                </CardHeader>
                <CardContent>
                  <TransactionTypesChart data={reportData} reportType={reportData.type} />
                </CardContent>
              </Card>

              {/* Amount Distribution Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-green-600" />
                    Amount Distribution by Type
                  </CardTitle>
                  <p className="text-sm text-gray-500">Stacked view of amounts by transaction type</p>
                </CardHeader>
                <CardContent>
                  <AmountDistributionChart data={reportData} />
                </CardContent>
              </Card>

              {/* Quantity Trends Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5 text-orange-600" />
                    Quantity Trends & Averages
                  </CardTitle>
                  <p className="text-sm text-gray-500">Quantity trends with average per transaction</p>
                </CardHeader>
                <CardContent>
                  <QuantityTrendsChart data={reportData} timeRange={reportData.timeRange} />
                </CardContent>
              </Card>
            </div>

            {/* Summary Metrics Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-indigo-600" />
                  Key Performance Metrics
                </CardTitle>
                <p className="text-sm text-gray-500">Visual overview of all key metrics</p>
              </CardHeader>
              <CardContent>
                <SummaryMetricsChart data={reportData} />
              </CardContent>
            </Card>

            {/* Detailed Data Table */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Detailed Breakdown Table
                </CardTitle>
                <p className="text-sm text-gray-500">Complete data breakdown by time period</p>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse border border-gray-300 dark:border-gray-600">
                    <thead>
                      <tr className="bg-gray-50 dark:bg-gray-800">
                        <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-medium">
                          {timeRange === "daily" ? "Date" : timeRange === "monthly" ? "Month" : "Year"}
                        </th>
                        <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-medium">
                          Transactions
                        </th>
                        <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-medium">
                          Amount ($)
                        </th>
                        <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-medium">
                          Quantity
                        </th>
                        <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-medium">
                          Purchases
                        </th>
                        <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-medium">
                          Transfers
                        </th>
                        {reportType === "chickens" && (
                          <>
                            <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-medium">
                              Allocations
                            </th>
                            <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-medium">
                              Buybacks
                            </th>
                          </>
                        )}
                        <th className="border border-gray-300 dark:border-gray-600 px-4 py-2 text-left font-medium">
                          Distributions
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {Object.entries(reportData.processed)
                        .sort(([a], [b]) => a.localeCompare(b))
                        .map(([period, data]) => (
                          <tr key={period} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                            <td className="border border-gray-300 dark:border-gray-600 px-4 py-2 font-medium">
                              {period}
                            </td>
                            <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                              {data.transactions}
                            </td>
                            <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                              ${data.totalAmount.toLocaleString()}
                            </td>
                            <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                              {data.totalQuantity.toLocaleString()}
                            </td>
                            <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{data.purchases}</td>
                            <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">{data.transfers}</td>
                            {reportType === "chickens" && (
                              <>
                                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                                  {data.allocations}
                                </td>
                                <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                                  {data.buybacks}
                                </td>
                              </>
                            )}
                            <td className="border border-gray-300 dark:border-gray-600 px-4 py-2">
                              {data.distributions}
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}

export default ReportsPage




/////////////////////////////////
// "use client"

// import { useState, useEffect } from "react"
// import { useLanguage } from "../../contexts/LanguageContext"
// import { Card, CardContent, CardHeader, CardTitle } from "../../components/feed-components/card"
// import Button from "../../components/Button"
// import { Download, BarChart3, TrendingUp, Package, Pill, Bird, FileText, Filter, RefreshCw } from "lucide-react"

// const ReportsPage = () => {
//   const { language, translations } = useLanguage()
//   const [reportType, setReportType] = useState("seeds") // seeds, drugs, chickens
//   const [timeRange, setTimeRange] = useState("monthly") // daily, monthly, yearly
//   const [startDate, setStartDate] = useState("")
//   const [endDate, setEndDate] = useState("")
//   const [reportData, setReportData] = useState(null)
//   const [loading, setLoading] = useState(false)
//   const [stats, setStats] = useState({
//     seeds: { purchases: 0, transfers: 0, totalAmount: 0 },
//     drugs: { purchases: 0, transfers: 0, totalAmount: 0 },
//     chickens: { purchases: 0, allocations: 0, buybacks: 0, distributions: 0 },
//   })

//   const t = (key) => {
//     if (!translations || !translations[language]) return key
//     return translations[language][key] || key
//   }

//   useEffect(() => {
//     loadInitialStats()
//     setDefaultDates()
//   }, [])

//   const setDefaultDates = () => {
//     const today = new Date()
//     const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)

//     setStartDate(firstDayOfMonth.toISOString().split("T")[0])
//     setEndDate(today.toISOString().split("T")[0])
//   }

//   const loadInitialStats = async () => {
//     try {
//       // Load seeds stats
//       const seedsResponse = await fetch("http://localhost:5432/api/v1/seed-purchases")
//       const seedsTransferResponse = await fetch("http://localhost:5432/api/v1/seed-transfers")

//       // Load drugs stats
//       const drugsResponse = await fetch("http://localhost:5432/api/v1/drug-purchases")
//       const drugsTransferResponse = await fetch("http://localhost:5432/api/v1/drug-transfers")

//       // Load chickens stats (you'll need to implement these endpoints)
//       const chickenPurchasesResponse = await fetch("http://localhost:5432/api/v1/chicken-purchases")
//       const chickenAllocationsResponse = await fetch("http://localhost:5432/api/v1/chicken-allocations")

//       const newStats = { ...stats }

//       // Process seeds data
//       if (seedsResponse.ok) {
//         const seedsData = await seedsResponse.json()
//         if (seedsData.success && Array.isArray(seedsData.data)) {
//           newStats.seeds.purchases = seedsData.data.length
//           newStats.seeds.totalAmount = seedsData.data.reduce((sum, item) => sum + (Number(item.total_amount) || 0), 0)
//         }
//       }

//       if (seedsTransferResponse.ok) {
//         const seedsTransferData = await seedsTransferResponse.json()
//         if (seedsTransferData.success && Array.isArray(seedsTransferData.data)) {
//           newStats.seeds.transfers = seedsTransferData.data.length
//         }
//       }

//       // Process drugs data
//       if (drugsResponse.ok) {
//         const drugsData = await drugsResponse.json()
//         if (drugsData.success && Array.isArray(drugsData.data)) {
//           newStats.drugs.purchases = drugsData.data.length
//           newStats.drugs.totalAmount = drugsData.data.reduce((sum, item) => sum + (Number(item.total_amount) || 0), 0)
//         }
//       }

//       if (drugsTransferResponse.ok) {
//         const drugsTransferData = await drugsTransferResponse.json()
//         if (drugsTransferData.success && Array.isArray(drugsTransferData.data)) {
//           newStats.drugs.transfers = drugsTransferData.data.length
//         }
//       }

//       setStats(newStats)
//     } catch (error) {
//       console.error("Error loading initial stats:", error)
//     }
//   }

//   const generateReport = async () => {
//     if (!startDate || !endDate) {
//       alert("Please select both start and end dates")
//       return
//     }

//     setLoading(true)
//     try {
//       let apiEndpoint = ""
//       let transferEndpoint = ""

//       switch (reportType) {
//         case "seeds":
//           apiEndpoint = "http://localhost:5432/api/v1/seed-purchases"
//           transferEndpoint = "http://localhost:5432/api/v1/seed-transfers"
//           break
//         case "drugs":
//           apiEndpoint = "http://localhost:5432/api/v1/drug-purchases"
//           transferEndpoint = "http://localhost:5432/api/v1/drug-transfers"
//           break
//         case "chickens":
//           apiEndpoint = "http://localhost:5432/api/v1/chicken-purchases"
//           transferEndpoint = "http://localhost:5432/api/v1/chicken-allocations"
//           break
//       }

//       const [purchasesResponse, transfersResponse] = await Promise.all([fetch(apiEndpoint), fetch(transferEndpoint)])

//       let purchasesData = []
//       let transfersData = []

//       if (purchasesResponse.ok) {
//         const purchasesResult = await purchasesResponse.json()
//         if (purchasesResult.success && Array.isArray(purchasesResult.data)) {
//           purchasesData = purchasesResult.data
//         }
//       }

//       if (transfersResponse.ok) {
//         const transfersResult = await transfersResponse.json()
//         if (transfersResult.success && Array.isArray(transfersResult.data)) {
//           transfersData = transfersResult.data
//         }
//       }

//       // Filter data by date range
//       const filteredPurchases = purchasesData.filter((item) => {
//         const itemDate = new Date(item.purchase_date || item.created_at)
//         return itemDate >= new Date(startDate) && itemDate <= new Date(endDate)
//       })

//       const filteredTransfers = transfersData.filter((item) => {
//         const itemDate = new Date(item.transfer_date || item.created_at)
//         return itemDate >= new Date(startDate) && itemDate <= new Date(endDate)
//       })

//       // Process data based on time range
//       const processedData = processDataByTimeRange(filteredPurchases, filteredTransfers, timeRange)

//       setReportData({
//         type: reportType,
//         timeRange,
//         startDate,
//         endDate,
//         purchases: filteredPurchases,
//         transfers: filteredTransfers,
//         processed: processedData,
//         summary: {
//           totalPurchases: filteredPurchases.length,
//           totalTransfers: filteredTransfers.length,
//           totalPurchaseAmount: filteredPurchases.reduce((sum, item) => sum + (Number(item.total_amount) || 0), 0),
//           totalQuantityPurchased: filteredPurchases.reduce((sum, item) => sum + (Number(item.quantity) || 0), 0),
//           totalQuantityTransferred: filteredTransfers.reduce((sum, item) => sum + (Number(item.quantity) || 0), 0),
//         },
//       })
//     } catch (error) {
//       console.error("Error generating report:", error)
//       alert("Error generating report. Please try again.")
//     } finally {
//       setLoading(false)
//     }
//   }

//   const processDataByTimeRange = (purchases, transfers, range) => {
//     const data = {}

//     purchases.forEach((item) => {
//       const date = new Date(item.purchase_date || item.created_at)
//       let key = ""

//       switch (range) {
//         case "daily":
//           key = date.toISOString().split("T")[0]
//           break
//         case "monthly":
//           key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`
//           break
//         case "yearly":
//           key = date.getFullYear().toString()
//           break
//       }

//       if (!data[key]) {
//         data[key] = { purchases: 0, transfers: 0, purchaseAmount: 0, quantity: 0 }
//       }

//       data[key].purchases += 1
//       data[key].purchaseAmount += Number(item.total_amount) || 0
//       data[key].quantity += Number(item.quantity) || 0
//     })

//     transfers.forEach((item) => {
//       const date = new Date(item.transfer_date || item.created_at)
//       let key = ""

//       switch (range) {
//         case "daily":
//           key = date.toISOString().split("T")[0]
//           break
//         case "monthly":
//           key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`
//           break
//         case "yearly":
//           key = date.getFullYear().toString()
//           break
//       }

//       if (!data[key]) {
//         data[key] = { purchases: 0, transfers: 0, purchaseAmount: 0, quantity: 0 }
//       }

//       data[key].transfers += 1
//     })

//     return data
//   }

//   const exportReport = (format) => {
//     if (!reportData) {
//       alert("Please generate a report first")
//       return
//     }

//     if (format === "csv") {
//       exportToCSV()
//     } else if (format === "json") {
//       exportToJSON()
//     }
//   }

//   const exportToCSV = () => {
//     const csvContent = generateCSVContent()
//     const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" })
//     const link = document.createElement("a")
//     const url = URL.createObjectURL(blob)
//     link.setAttribute("href", url)
//     link.setAttribute("download", `${reportType}_report_${timeRange}_${startDate}_to_${endDate}.csv`)
//     link.style.visibility = "hidden"
//     document.body.appendChild(link)
//     link.click()
//     document.body.removeChild(link)
//   }

//   const exportToJSON = () => {
//     const jsonContent = JSON.stringify(reportData, null, 2)
//     const blob = new Blob([jsonContent], { type: "application/json;charset=utf-8;" })
//     const link = document.createElement("a")
//     const url = URL.createObjectURL(blob)
//     link.setAttribute("href", url)
//     link.setAttribute("download", `${reportType}_report_${timeRange}_${startDate}_to_${endDate}.json`)
//     link.style.visibility = "hidden"
//     document.body.appendChild(link)
//     link.click()
//     document.body.removeChild(link)
//   }

//   const generateCSVContent = () => {
//     let csv = `${reportType.toUpperCase()} Report - ${timeRange.toUpperCase()}\n`
//     csv += `Period: ${startDate} to ${endDate}\n\n`

//     if (timeRange === "daily") {
//       csv += "Date,Purchases,Transfers,Purchase Amount,Quantity\n"
//     } else if (timeRange === "monthly") {
//       csv += "Month,Purchases,Transfers,Purchase Amount,Quantity\n"
//     } else {
//       csv += "Year,Purchases,Transfers,Purchase Amount,Quantity\n"
//     }

//     Object.entries(reportData.processed).forEach(([key, data]) => {
//       csv += `${key},${data.purchases},${data.transfers},${data.purchaseAmount.toFixed(2)},${data.quantity}\n`
//     })

//     return csv
//   }

//   const reportTypeOptions = [
//     { value: "seeds", label: "Seeds", icon: Package, color: "text-blue-600", bgColor: "bg-blue-50" },
//     { value: "drugs", label: "Drugs", icon: Pill, color: "text-purple-600", bgColor: "bg-purple-50" },
//     { value: "chickens", label: "Chickens", icon: Bird, color: "text-green-600", bgColor: "bg-green-50" },
//   ]

//   const timeRangeOptions = [
//     { value: "daily", label: "Daily" },
//     { value: "monthly", label: "Monthly" },
//     { value: "yearly", label: "Yearly" },
//   ]

//   return (
//     <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === "ps" ? "rtl" : "ltr"}`}>
//       <div className="max-w-7xl mx-auto space-y-6">
//         {/* Header */}
//         <div
//           className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === "ps" ? "flex-row-reverse" : ""}`}
//         >
//           <div>
//             <h1 className="text-3xl font-bold text-gray-900 dark:text-white">{t("reports") || "Reports"}</h1>
//             <p className="text-gray-600 dark:text-gray-400">
//               Generate comprehensive reports for seeds, drugs, and chickens
//             </p>
//           </div>
//           <Button onClick={loadInitialStats} variant="secondary" className="flex items-center gap-2">
//             <RefreshCw className="h-4 w-4" />
//             Refresh Stats
//           </Button>
//         </div>

//         {/* Quick Stats */}
//         <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
//           <Card className="bg-gradient-to-br from-blue-500/10 to-blue-500/5 border-blue-500/20">
//             <CardContent className="p-6">
//               <div className="flex items-center justify-between">
//                 <div>
//                   <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Seeds</p>
//                   <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{stats.seeds.purchases}</h3>
//                   <p className="text-xs text-blue-600">${stats.seeds.totalAmount.toFixed(2)} total</p>
//                 </div>
//                 <div className="p-3 bg-blue-500/10 rounded-full">
//                   <Package className="h-6 w-6 text-blue-500" />
//                 </div>
//               </div>
//             </CardContent>
//           </Card>

//           <Card className="bg-gradient-to-br from-purple-500/10 to-purple-500/5 border-purple-500/20">
//             <CardContent className="p-6">
//               <div className="flex items-center justify-between">
//                 <div>
//                   <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Drugs</p>
//                   <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{stats.drugs.purchases}</h3>
//                   <p className="text-xs text-purple-600">${stats.drugs.totalAmount.toFixed(2)} total</p>
//                 </div>
//                 <div className="p-3 bg-purple-500/10 rounded-full">
//                   <Pill className="h-6 w-6 text-purple-500" />
//                 </div>
//               </div>
//             </CardContent>
//           </Card>

//           <Card className="bg-gradient-to-br from-green-500/10 to-green-500/5 border-green-500/20">
//             <CardContent className="p-6">
//               <div className="flex items-center justify-between">
//                 <div>
//                   <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Chickens</p>
//                   <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{stats.chickens.purchases}</h3>
//                   <p className="text-xs text-green-600">{stats.chickens.allocations} allocations</p>
//                 </div>
//                 <div className="p-3 bg-green-500/10 rounded-full">
//                   <Bird className="h-6 w-6 text-green-500" />
//                 </div>
//               </div>
//             </CardContent>
//           </Card>
//         </div>

//         {/* Report Configuration */}
//         <Card>
//           <CardHeader>
//             <CardTitle className="flex items-center gap-2">
//               <Filter className="h-5 w-5" />
//               Report Configuration
//             </CardTitle>
//           </CardHeader>
//           <CardContent className="space-y-6">
//             {/* Report Type Selection */}
//             <div>
//               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Report Type</label>
//               <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
//                 {reportTypeOptions.map((option) => {
//                   const IconComponent = option.icon
//                   return (
//                     <button
//                       key={option.value}
//                       onClick={() => setReportType(option.value)}
//                       className={`p-4 rounded-lg border-2 transition-all duration-200 ${
//                         reportType === option.value
//                           ? `border-gray-400 ${option.bgColor}`
//                           : "border-gray-200 hover:border-gray-300"
//                       }`}
//                     >
//                       <div className="flex items-center space-x-3">
//                         <div className={`p-2 rounded-lg ${option.bgColor}`}>
//                           <IconComponent className={`h-5 w-5 ${option.color}`} />
//                         </div>
//                         <span className="font-medium text-gray-900 dark:text-white">{option.label}</span>
//                       </div>
//                     </button>
//                   )
//                 })}
//               </div>
//             </div>

//             {/* Time Range Selection */}
//             <div>
//               <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Time Range</label>
//               <div className="flex flex-wrap gap-2">
//                 {timeRangeOptions.map((option) => (
//                   <button
//                     key={option.value}
//                     onClick={() => setTimeRange(option.value)}
//                     className={`px-4 py-2 rounded-lg border transition-all duration-200 ${
//                       timeRange === option.value
//                         ? "border-blue-500 bg-blue-50 text-blue-700"
//                         : "border-gray-200 hover:border-gray-300"
//                     }`}
//                   >
//                     {option.label}
//                   </button>
//                 ))}
//               </div>
//             </div>

//             {/* Date Range Selection */}
//             <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Start Date</label>
//                 <input
//                   type="date"
//                   value={startDate}
//                   onChange={(e) => setStartDate(e.target.value)}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
//                 />
//               </div>
//               <div>
//                 <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">End Date</label>
//                 <input
//                   type="date"
//                   value={endDate}
//                   onChange={(e) => setEndDate(e.target.value)}
//                   className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
//                 />
//               </div>
//             </div>

//             {/* Generate Report Button */}
//             <div className="flex flex-wrap gap-3">
//               <Button onClick={generateReport} disabled={loading} className="flex items-center gap-2">
//                 {loading ? <RefreshCw className="h-4 w-4 animate-spin" /> : <BarChart3 className="h-4 w-4" />}
//                 {loading ? "Generating..." : "Generate Report"}
//               </Button>

//               {reportData && (
//                 <>
//                   <Button onClick={() => exportReport("csv")} variant="secondary" className="flex items-center gap-2">
//                     <Download className="h-4 w-4" />
//                     Export CSV
//                   </Button>
//                   <Button onClick={() => exportReport("json")} variant="secondary" className="flex items-center gap-2">
//                     <FileText className="h-4 w-4" />
//                     Export JSON
//                   </Button>
//                 </>
//               )}
//             </div>
//           </CardContent>
//         </Card>

//         {/* Report Results */}
//         {reportData && (
//           <div className="space-y-6">
//             {/* Report Summary */}
//             <Card>
//               <CardHeader>
//                 <CardTitle className="flex items-center gap-2">
//                   <TrendingUp className="h-5 w-5" />
//                   Report Summary - {reportData.type.toUpperCase()} ({reportData.timeRange})
//                 </CardTitle>
//                 <p className="text-sm text-gray-500">
//                   Period: {reportData.startDate} to {reportData.endDate}
//                 </p>
//               </CardHeader>
//               <CardContent>
//                 <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
//                   <div className="text-center p-4 bg-blue-50 rounded-lg">
//                     <div className="text-2xl font-bold text-blue-600">{reportData.summary.totalPurchases}</div>
//                     <div className="text-sm text-blue-700">Total Purchases</div>
//                   </div>
//                   <div className="text-center p-4 bg-green-50 rounded-lg">
//                     <div className="text-2xl font-bold text-green-600">{reportData.summary.totalTransfers}</div>
//                     <div className="text-sm text-green-700">Total Transfers</div>
//                   </div>
//                   <div className="text-center p-4 bg-purple-50 rounded-lg">
//                     <div className="text-2xl font-bold text-purple-600">
//                       ${reportData.summary.totalPurchaseAmount.toFixed(2)}
//                     </div>
//                     <div className="text-sm text-purple-700">Total Amount</div>
//                   </div>
//                   <div className="text-center p-4 bg-orange-50 rounded-lg">
//                     <div className="text-2xl font-bold text-orange-600">
//                       {reportData.summary.totalQuantityPurchased}
//                     </div>
//                     <div className="text-sm text-orange-700">Total Quantity</div>
//                   </div>
//                 </div>
//               </CardContent>
//             </Card>

//             {/* Detailed Data Table */}
//             <Card>
//               <CardHeader>
//                 <CardTitle>Detailed Breakdown</CardTitle>
//               </CardHeader>
//               <CardContent>
//                 <div className="overflow-x-auto">
//                   <table className="w-full border-collapse border border-gray-300">
//                     <thead>
//                       <tr className="bg-gray-50">
//                         <th className="border border-gray-300 px-4 py-2 text-left">
//                           {timeRange === "daily" ? "Date" : timeRange === "monthly" ? "Month" : "Year"}
//                         </th>
//                         <th className="border border-gray-300 px-4 py-2 text-left">Purchases</th>
//                         <th className="border border-gray-300 px-4 py-2 text-left">Transfers</th>
//                         <th className="border border-gray-300 px-4 py-2 text-left">Amount ($)</th>
//                         <th className="border border-gray-300 px-4 py-2 text-left">Quantity</th>
//                       </tr>
//                     </thead>
//                     <tbody>
//                       {Object.entries(reportData.processed).map(([period, data]) => (
//                         <tr key={period} className="hover:bg-gray-50">
//                           <td className="border border-gray-300 px-4 py-2">{period}</td>
//                           <td className="border border-gray-300 px-4 py-2">{data.purchases}</td>
//                           <td className="border border-gray-300 px-4 py-2">{data.transfers}</td>
//                           <td className="border border-gray-300 px-4 py-2">${data.purchaseAmount.toFixed(2)}</td>
//                           <td className="border border-gray-300 px-4 py-2">{data.quantity}</td>
//                         </tr>
//                       ))}
//                     </tbody>
//                   </table>
//                 </div>
//               </CardContent>
//             </Card>
//           </div>
//         )}
//       </div>
//     </div>
//   )
// }

// export default ReportsPage




////////////////////////
/////////////////////////////////
///////////////////////
// import React, { useState } from 'react';
// import { useLanguage } from '../../contexts/LanguageContext';
// import { useReports } from '../../contexts/ReportContext';
// import { Card, Row, Col, Button, DatePicker, Table, Space, Select } from 'antd';
// import { DownloadOutlined, BarChartOutlined, LineChartOutlined, PieChartOutlined } from '@ant-design/icons';
// import { Line, Bar, Pie } from 'react-chartjs-2';
// import {
//   Chart as ChartJS,
//   CategoryScale,
//   LinearScale,
//   PointElement,
//   LineElement,
//   BarElement,
//   ArcElement,
//   Title,
//   Tooltip,
//   Legend,
// } from 'chart.js';

// ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, BarElement, ArcElement, Title, Tooltip, Legend);

// const ReportsPage = () => {
//   const { language, translations } = useLanguage();
//   const { getWeeklyReport, getMonthlyReport, getAnnualReport } = useReports();
//   const [reportType, setReportType] = useState('weekly');
//   const [startDate, setStartDate] = useState(null);
//   const [endDate, setEndDate] = useState(null);
//   const [reportData, setReportData] = useState(null);

//   // Get translation function
//   const t = (key) => translations[language][key];

//   const handleGenerateReport = () => {
//     if (!startDate || !endDate) return;

//     let report;
//     switch (reportType) {
//       case 'weekly':
//         report = getWeeklyReport(startDate, endDate);
//         break;
//       case 'monthly':
//         report = getMonthlyReport(startDate, endDate);
//         break;
//       case 'annual':
//         report = getAnnualReport(startDate.getFullYear());
//         break;
//       default:
//         return;
//     }
//     setReportData(report);
//   };

//   const handleExport = (format) => {
//     // Implement export functionality
//     console.log(`Exporting to ${format} format`);
//   };

//   const chartData = {
//     labels: reportData?.data?.labels || [],
//     datasets: [
//       {
//         label: t('revenue'),
//         data: reportData?.data?.revenue || [],
//         borderColor: 'rgb(75, 192, 192)',
//         backgroundColor: 'rgba(75, 192, 192, 0.5)',
//       },
//       {
//         label: t('expenses'),
//         data: reportData?.data?.expenses || [],
//         borderColor: 'rgb(255, 99, 132)',
//         backgroundColor: 'rgba(255, 99, 132, 0.5)',
//       },
//     ],
//   };

//   return (
//     <div className="reports-page">
//       <Card title={t('reports')}>
//         <Space style={{ marginBottom: 16 }}>
//           <Select
//             value={reportType}
//             onChange={setReportType}
//             options={[
//               { value: 'weekly', label: t('weekly_report') },
//               { value: 'monthly', label: t('monthly_report') },
//               { value: 'annual', label: t('annual_report') },
//             ]}
//           />
//           <DatePicker.RangePicker
//             onChange={(dates) => {
//               setStartDate(dates[0]);
//               setEndDate(dates[1]);
//             }}
//           />
//           <Button type="primary" onClick={handleGenerateReport}>
//             {t('generate_report')}
//           </Button>
//           <Button icon={<DownloadOutlined />} onClick={() => handleExport('pdf')}>
//             {t('export_pdf')}
//           </Button>
//           <Button icon={<DownloadOutlined />} onClick={() => handleExport('excel')}>
//             {t('export_excel')}
//           </Button>
//         </Space>

//         {reportData && (
//           <Row gutter={[16, 16]}>
//             <Col span={24}>
//               <Card title={t('revenue_vs_expenses')}>
//                 <Line data={chartData} />
//               </Card>
//             </Col>
//             <Col span={12}>
//               <Card title={t('sales_by_category')}>
//                 <Pie data={chartData} />
//               </Card>
//             </Col>
//             <Col span={12}>
//               <Card title={t('monthly_comparison')}>
//                 <Bar data={chartData} />
//               </Card>
//             </Col>
//           </Row>
//         )}
//       </Card>
//     </div>
//   );
// };

// export default ReportsPage;
