import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Save, X } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import Button from '../../components/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';

const EditDrugPurchasePage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { language, t } = useLanguage();

  const [formData, setFormData] = useState({
    drug_id: null,
    drug_name: '',
    quantity: '',
    price_per_unit: '',
    supplier: '',
    purchase_date: '',
    notes: '',
    status: '',
  });
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState({});
  const [submitError, setSubmitError] = useState(null);
  const [fetchError, setFetchError] = useState(null);
  const [purchases, setPurchases] = useState([]);



  useEffect(() => {
    const fetchPurchase = async () => {
      setLoading(true);
      setFetchError(null);
      try {
        console.log('Fetching purchase with ID:', id);
        const res = await fetch(`http://localhost:5432/api/v1/drug-purchases/${id}`);
        console.log('Response status:', res.status);

        if (!res.ok) {
          const errorText = await res.text();
          console.error('Response error:', errorText);
          throw new Error(`Failed to fetch purchase: ${res.status}`);
        }

        const json = await res.json();
        console.log('Response data:', json);

        if (!json.success || !json.data) {
          throw new Error('Purchase not found');
        }

        setFormData({
          drug_id: json.data.drug_id || null,
          drug_name: json.data.drug_name || '',
          quantity: json.data.quantity?.toString() || '',
          price_per_unit: json.data.price_per_unit?.toString() || '',
          supplier: json.data.supplier || '',
          purchase_date: json.data.purchase_date ? json.data.purchase_date.slice(0, 10) : '',
          notes: json.data.notes || '',
          status: json.data.status || '',
        });
      } catch (err) {
        setFetchError(err.message || t('drugs.purchases.edit.loading_error'));
      } finally {
        setLoading(false);
      }
    };
    fetchPurchase();
  }, [id]);

  useEffect(() => {
    fetch('http://localhost:5432/api/v1/drug-purchases')
      .then(res => res.json())
      .then(json => setPurchases(json.data || []));
  }, []);

  const allDrugsFromPurchases = purchases;

  const validateForm = () => {
    const newErrors = {};
    if (!formData.drug_name.trim()) newErrors.drug_name = t('drugs.purchases.edit.validation.drug_name_required');
    if (!formData.quantity || parseInt(formData.quantity) < 1) newErrors.quantity = t('drugs.purchases.edit.validation.quantity_required');
    if (!formData.price_per_unit || parseFloat(formData.price_per_unit) < 0) newErrors.price_per_unit = t('drugs.purchases.edit.validation.price_required');
    if (!formData.supplier.trim()) newErrors.supplier = t('drugs.purchases.edit.validation.supplier_required');
    if (!formData.purchase_date) newErrors.purchase_date = t('drugs.purchases.edit.validation.purchase_date_required');
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitError(null);
    if (!validateForm()) return;
    try {
      setLoading(true);
      const purchaseData = {
        ...formData,
        quantity: parseInt(formData.quantity),
        price_per_unit: parseFloat(formData.price_per_unit),
        total_amount: parseInt(formData.quantity) * parseFloat(formData.price_per_unit),
        status: formData.status || 'Pending',
      };

      console.log('Submitting purchase data:', purchaseData);

      const response = await fetch(`http://localhost:5432/api/v1/drug-purchases/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(purchaseData),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Error response:', errorData);
        throw new Error(errorData.error || `Failed to update purchase (${response.status})`);
      }

      const result = await response.json();
      console.log('Success response:', result);

      // Navigate back with success message
      navigate('/admin/drugs/purchases?success=purchase_updated');
    } catch (error) {
      console.error('Submit error:', error);
      setSubmitError(error.message || t('drugs.purchases.edit.error_message'));
    } finally {
      setLoading(false);
    }
  };

  const totalAmount = formData.quantity && formData.price_per_unit
    ? (parseInt(formData.quantity) * parseFloat(formData.price_per_unit)).toFixed(2)
    : '0.00';

  if (loading) {
    return <div className="p-8 text-center text-lg">Loading...</div>;
  }
  if (fetchError) {
    return <div className="p-8 text-center text-red-600">{fetchError}</div>;
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto space-y-6">
        <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => navigate('/admin/drugs/purchases')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('drugs.purchases.back_to_drugs')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('drugs.purchases.edit.title')}</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('drugs.purchases.edit.description')}</p>
          </div>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>{t('drugs.purchases.edit.form_title')}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('drugs.purchases.add.drug_name_label')}</label>
                  <select
                    name="drug_name"
                    value={formData.drug_name}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.drug_name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  >
                    <option value="">{t('drugs.purchases.add.drug_name_placeholder')}</option>
                    {allDrugsFromPurchases.map((drug, idx) => (
                      <option key={drug.id || idx} value={drug.drug_name}>
                        {drug.drug_name}
                      </option>
                    ))}
                  </select>
                  {errors.drug_name && <p className="text-red-500 text-sm mt-1">{errors.drug_name}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('drugs.purchases.add.quantity_label')}</label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleChange}
                    min="1"
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.quantity ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    placeholder={t('drugs.purchases.add.quantity_placeholder')}
                  />
                  {errors.quantity && <p className="text-red-500 text-sm mt-1">{errors.quantity}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('drugs.purchases.add.price_per_unit_label')}</label>
                  <input
                    type="number"
                    name="price_per_unit"
                    value={formData.price_per_unit}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.price_per_unit ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    placeholder={t('drugs.purchases.add.price_per_unit_placeholder')}
                  />
                  {errors.price_per_unit && <p className="text-red-500 text-sm mt-1">{errors.price_per_unit}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('drugs.purchases.add.total_amount_label')}</label>
                  <input
                    type="text"
                    value={`$${totalAmount}`}
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('drugs.purchases.add.supplier_label')}</label>
                  <input
                    type="text"
                    name="supplier"
                    value={formData.supplier}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.supplier ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    placeholder={t('drugs.purchases.add.supplier_placeholder')}
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  {errors.supplier && <p className="text-red-500 text-sm mt-1">{errors.supplier}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('drugs.purchases.add.purchase_date_label')}</label>
                  <input
                    type="date"
                    name="purchase_date"
                    value={formData.purchase_date}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.purchase_date ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                  />
                  {errors.purchase_date && <p className="text-red-500 text-sm mt-1">{errors.purchase_date}</p>}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('drugs.purchases.add.notes_label')}</label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                  placeholder={t('drugs.purchases.add.notes_placeholder')}
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>
              {submitError && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 text-sm">{submitError}</p>
                </div>
              )}
              <div className={`flex gap-4 pt-6 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <Button type="submit" disabled={loading} className="flex items-center gap-2">
                  <Save className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                  {loading ? t('drugs.purchases.edit.saving_button') : t('drugs.purchases.edit.save_button')}
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => navigate('/admin/drugs/purchases')}
                  className="flex items-center gap-2"
                >
                  <X className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                  {t('drugs.purchases.cancel')}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EditDrugPurchasePage; 