import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  Download,
  Filter,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  Save,
  Printer,
  FileText
} from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import Button from '../../components/Button';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart as RechartsPieChart,
  Pie,
  Cell,
  LineChart as RechartsLineChart,
  Line,
  ComposedChart,
  Area
} from 'recharts';
import { format } from 'date-fns';

const DrugReportsPage = () => {
  const navigate = useNavigate();
  const { language, translations } = useLanguage();
  const [reportData, setReportData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState('all');
  const [chartTimeFilter, setChartTimeFilter] = useState('month');
  const [summary, setSummary] = useState({
    totalPurchases: 0,
    completedTransfers: 0,
    totalInvestment: 0,
    totalValue: 0,
    totalProfit: 0,
    profitMargin: 0
  });

  const t = (key) => {
    if (!translations || !translations[language]) return key;

    // Handle nested keys like 'drugs.reports.back'
    const keys = key.split('.');
    let value = translations[language];

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key; // Return original key if translation not found
      }
    }

    return typeof value === 'string' ? value : key;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'AFN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount || 0).replace('AFN', 'AFN ');
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch (error) {
      return 'Invalid Date';
    }
  };

  useEffect(() => {
    fetchReportData();
  }, []);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      
      // Fetch purchases and transfers data
      const [purchasesResponse, transfersResponse] = await Promise.all([
        fetch('http://localhost:5432/api/v1/drug-purchases'),
        fetch('http://localhost:5432/api/v1/drug-transfers')
      ]);

      const purchasesData = await purchasesResponse.json();
      const transfersData = await transfersResponse.json();

      if (purchasesData.success && transfersData.success) {
        const purchases = purchasesData.data || [];
        const transfers = transfersData.data || [];

        // Process data to create comprehensive reports
        const processedData = purchases.map(purchase => {
          const relatedTransfers = transfers.filter(transfer =>
            transfer.purchase_id === purchase.id
          );

          const totalTransferred = relatedTransfers.reduce((sum, transfer) =>
            sum + (transfer.quantity || 0), 0
          );

          const remainingQuantity = (purchase.remaining_quantity !== undefined) ?
            purchase.remaining_quantity :
            ((purchase.quantity || 0) - totalTransferred);

          // Calculate transfer revenue using actual transfer prices
          const transferRevenue = relatedTransfers.reduce((sum, transfer) =>
            sum + (parseFloat(transfer.total_price) || 0), 0
          );

          // Calculate total profit from transfers
          const totalTransferProfit = relatedTransfers.reduce((sum, transfer) =>
            sum + (parseFloat(transfer.total_profit) || 0), 0
          );

          // Calculate investment cost and remaining value
          const investmentCost = parseFloat(purchase.total_amount) || 0;
          const purchasePricePerUnit = parseFloat(purchase.price_per_unit) || 0;
          const remainingValue = remainingQuantity * purchasePricePerUnit;
          const totalCurrentValue = transferRevenue + remainingValue;
          const totalProfit = totalTransferProfit; // Use actual transfer profits
          const profitMargin = investmentCost > 0 ? (totalProfit / investmentCost) * 100 : 0;

          return {
            id: purchase.id,
            drugName: purchase.drug_name || 'N/A',
            supplier: purchase.supplier || 'N/A',
            purchaseDate: purchase.purchase_date,
            quantity: parseInt(purchase.quantity) || 0,
            pricePerUnit: parseFloat(purchase.price_per_unit) || 0,
            totalAmount: parseFloat(purchase.total_amount) || 0,
            transferred: totalTransferred,
            remaining: remainingQuantity,
            transferRevenue: transferRevenue,
            remainingValue: remainingValue,
            currentValue: totalCurrentValue,
            profit: totalProfit,
            profitMargin: profitMargin,
            status: remainingQuantity === 0 ? 'completed' :
                   totalTransferred > 0 ? 'in_progress' : 'pending',
            transfers: relatedTransfers
          };
        });

        setReportData(processedData);

        // Calculate summary
        const totalPurchases = processedData.length;
        const completedTransfers = processedData.filter(item => item.status === 'completed').length;
        const totalInvestment = processedData.reduce((sum, item) => sum + item.totalAmount, 0);
        const totalValue = processedData.reduce((sum, item) => sum + item.currentValue, 0);
        const totalProfit = processedData.reduce((sum, item) => sum + item.profit, 0);
        const profitMargin = totalInvestment > 0 ? (totalProfit / totalInvestment) * 100 : 0;

        setSummary({
          totalPurchases,
          completedTransfers,
          totalInvestment,
          totalValue,
          totalProfit,
          profitMargin
        });
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredData = reportData.filter(item => {
    if (statusFilter === 'all') return true;
    return item.status === statusFilter;
  });

  const exportToCSV = () => {
    const headers = [
      t('drugs.reports.drug_name'), t('drugs.reports.supplier'), t('drugs.reports.purchase_date'),
      t('drugs.reports.quantity'), 'Price/Unit', t('drugs.reports.investment'),
      t('drugs.reports.transferred'), t('drugs.reports.remaining'), t('drugs.reports.current_value_table'),
      t('drugs.reports.profit_loss'), t('drugs.reports.status')
    ];

    // Escape CSV values to handle commas and quotes
    const escapeCSV = (value) => {
      if (value === null || value === undefined) return '';
      const stringValue = String(value);
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    };

    const csvContent = [
      headers.map(escapeCSV).join(','),
      ...filteredData.map(item => [
        escapeCSV(item.drugName),
        escapeCSV(item.supplier),
        escapeCSV(formatDate(item.purchaseDate)),
        escapeCSV(item.quantity),
        escapeCSV(item.pricePerUnit),
        escapeCSV(item.totalAmount),
        escapeCSV(item.transferred),
        escapeCSV(item.remaining),
        escapeCSV(item.currentValue),
        escapeCSV(item.profit),
        escapeCSV(item.status === 'completed' ? t('drugs.reports.completed') :
        item.status === 'in_progress' ? t('drugs.reports.in_progress') :
        t('drugs.reports.pending'))
      ].join(','))
    ].join('\n');

    // Add UTF-8 BOM for proper Unicode handling
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `drug-reports-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const exportToPDF = () => {
    // Create PDF content
    const printContent = `
      <!DOCTYPE html>
      <html lang="${language}">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${t('drugs.reports.drug_reports')} - ${new Date().toLocaleDateString()}</title>
          <style>
            @import url('https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;700&family=Noto+Sans+Arabic:wght@400;700&display=swap');

            body {
              font-family: 'Noto Sans', 'Noto Sans Arabic', Arial, sans-serif;
              margin: 20px;
              direction: ${language === 'ps' ? 'rtl' : 'ltr'};
              unicode-bidi: embed;
            }
            .header { text-align: center; margin-bottom: 30px; }
            .summary { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 30px; }
            .summary-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
            .summary-card h3 { margin: 0 0 10px 0; font-size: 14px; color: #666; }
            .summary-card .value { font-size: 24px; font-weight: bold; margin: 5px 0; }
            .summary-card .subtitle { font-size: 12px; color: #888; }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 20px;
              font-family: 'Noto Sans', 'Noto Sans Arabic', Arial, sans-serif;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: ${language === 'ps' ? 'right' : 'left'};
              font-size: 12px;
              unicode-bidi: embed;
            }
            th { background-color: #f5f5f5; font-weight: bold; }
            .profit-positive { color: #388E3C; }
            .profit-negative { color: #D32F2F; }
            .status-completed { background-color: #E8F5E8; color: #2E7D32; }
            .status-progress { background-color: #FFF3E0; color: #F57C00; }
            .status-pending { background-color: #F5F5F5; color: #616161; }
            @media print {
              body { margin: 0; }
              .no-print { display: none; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${t('drugs.reports.drug_reports')}</h1>
            <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
          </div>

          <div class="summary">
            <div class="summary-card">
              <h3>${t('drugs.reports.total_purchases')}</h3>
              <div class="value">${summary.totalPurchases}</div>
              <div class="subtitle">${summary.completedTransfers} ${t('drugs.reports.completed_transfers')}</div>
            </div>
            <div class="summary-card">
              <h3>${t('drugs.reports.total_investment')}</h3>
              <div class="value">${formatCurrency(summary.totalInvestment)}</div>
              <div class="subtitle">${t('drugs.reports.purchase_costs')}</div>
            </div>
            <div class="summary-card">
              <h3>${t('drugs.reports.current_value')}</h3>
              <div class="value">${formatCurrency(summary.totalValue)}</div>
              <div class="subtitle">${t('drugs.reports.market_value')}</div>
            </div>
            <div class="summary-card">
              <h3>${t('drugs.reports.total_profit')}</h3>
              <div class="value ${summary.totalProfit >= 0 ? 'profit-positive' : 'profit-negative'}">${formatCurrency(summary.totalProfit)}</div>
              <div class="subtitle">${summary.profitMargin.toFixed(1)}% ${t('drugs.reports.profit_margin')}</div>
            </div>
          </div>

          <table>
            <thead>
              <tr>
                <th>${t('drugs.reports.drug_name')}</th>
                <th>${t('drugs.reports.supplier')}</th>
                <th>${t('drugs.reports.purchase_date')}</th>
                <th>${t('drugs.reports.quantity')}</th>
                <th>${t('drugs.reports.transferred')}</th>
                <th>${t('drugs.reports.remaining')}</th>
                <th>${t('drugs.reports.investment')}</th>
                <th>${t('drugs.reports.current_value_table')}</th>
                <th>${t('drugs.reports.profit_loss')}</th>
                <th>${t('drugs.reports.status')}</th>
              </tr>
            </thead>
            <tbody>
              ${filteredData.map(item => `
                <tr>
                  <td>${item.drugName}</td>
                  <td>${item.supplier}</td>
                  <td>${formatDate(item.purchaseDate)}</td>
                  <td>${item.quantity}</td>
                  <td>${item.transferred}</td>
                  <td>${item.remaining}</td>
                  <td>${formatCurrency(item.totalAmount)}</td>
                  <td>${formatCurrency(item.currentValue)}</td>
                  <td class="${item.profit >= 0 ? 'profit-positive' : 'profit-negative'}">${formatCurrency(item.profit)} (${item.profitMargin.toFixed(1)}%)</td>
                  <td class="status-${item.status.replace('_', '-')}">${
                    item.status === 'completed' ? t('drugs.reports.completed') :
                    item.status === 'in_progress' ? t('drugs.reports.in_progress') :
                    t('drugs.reports.pending')
                  }</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </body>
      </html>
    `;

    // Open new window and print
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  const exportToPDFFile = async () => {
    try {
      // Dynamic import to avoid build issues if packages aren't installed yet
      const jsPDF = (await import('jspdf')).default;
      const autoTable = (await import('jspdf-autotable')).default;

      const doc = new jsPDF('landscape'); // Use landscape orientation for better table fit
      const pageWidth = doc.internal.pageSize.width;
      const margin = 15;

      // Set font for Unicode support (use built-in fonts that support more characters)
      doc.setFont('helvetica', 'normal');

      // Add title
      doc.setFontSize(20);
      doc.setTextColor(40, 40, 40);
      const title = t('drugs.reports.drug_reports');
      doc.text(title, margin, 30);

      // Add date
      doc.setFontSize(12);
      doc.setTextColor(100, 100, 100);
      doc.text(`Generated on: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, margin, 45);

      // Add summary section
      doc.setFontSize(16);
      doc.setTextColor(40, 40, 40);
      doc.text(t('drugs.reports.detailed_process_analysis'), margin, 65);

      const summaryData = [
        [t('drugs.reports.total_purchases'), summary.totalPurchases.toString()],
        [t('drugs.reports.completed_transfers'), summary.completedTransfers.toString()],
        [t('drugs.reports.total_investment'), formatCurrency(summary.totalInvestment)],
        [t('drugs.reports.current_value'), formatCurrency(summary.totalValue)],
        [t('drugs.reports.total_profit'), formatCurrency(summary.totalProfit)],
        [t('drugs.reports.profit_margin'), `${summary.profitMargin.toFixed(1)}%`]
      ];

      autoTable(doc, {
        startY: 75,
        head: [['Metric', 'Value']],
        body: summaryData,
        theme: 'grid',
        headStyles: { fillColor: [255, 107, 0] },
        margin: { left: margin, right: margin }
      });

      // Add detailed data table
      const finalY = doc.lastAutoTable.finalY + 20;
      doc.setFontSize(16);
      doc.text(t('drugs.reports.detailed_process_analysis'), margin, finalY);

      const tableData = filteredData.map(item => [
        item.drugName,
        item.supplier,
        formatDate(item.purchaseDate),
        item.quantity.toString(),
        item.transferred.toString(),
        item.remaining.toString(),
        formatCurrency(item.totalAmount),
        formatCurrency(item.currentValue),
        formatCurrency(item.profit),
        `${item.profitMargin.toFixed(1)}%`,
        item.status === 'completed' ? t('drugs.reports.completed') :
        item.status === 'in_progress' ? t('drugs.reports.in_progress') :
        t('drugs.reports.pending')
      ]);

      autoTable(doc, {
        startY: finalY + 10,
        head: [[t('drugs.reports.drug_name'), t('drugs.reports.supplier'), t('drugs.reports.purchase_date'), t('drugs.reports.quantity'), t('drugs.reports.transferred'), t('drugs.reports.remaining'), t('drugs.reports.investment'), t('drugs.reports.current_value_table'), t('drugs.reports.profit_loss'), 'Margin %', t('drugs.reports.status')]],
        body: tableData,
        theme: 'grid',
        headStyles: {
          fillColor: [255, 107, 0],
          fontSize: 9,
          font: 'helvetica',
          fontStyle: 'bold'
        },
        margin: { left: 15, right: 15 },
        styles: {
          fontSize: 8,
          cellPadding: 3,
          overflow: 'linebreak',
          cellWidth: 'wrap',
          font: 'helvetica',
          fontStyle: 'normal'
        },
        columnStyles: {
          0: { cellWidth: 25 }, // Drug Name
          1: { cellWidth: 22 }, // Supplier
          2: { cellWidth: 22 }, // Date
          3: { cellWidth: 18 }, // Quantity
          4: { cellWidth: 18 }, // Transferred
          5: { cellWidth: 18 }, // Remaining
          6: { cellWidth: 22 }, // Investment
          7: { cellWidth: 22 }, // Current Value
          8: { cellWidth: 22 }, // Profit/Loss
          9: { cellWidth: 18 }, // Margin
          10: { cellWidth: 20 } // Status
        },
        tableWidth: 'auto',
        showHead: 'everyPage'
      });

      // Save the PDF
      const fileName = `drug-reports-${new Date().toISOString().split('T')[0]}.pdf`;
      doc.save(fileName);

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('PDF export requires additional packages. Please install jspdf and jspdf-autotable packages.');
    }
  };

  const getProfitIcon = (profit) => {
    if (profit > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (profit < 0) return <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />;
    return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      completed: { bg: 'bg-green-100', text: 'text-green-800', label: t('drugs.reports.completed') },
      in_progress: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: t('drugs.reports.in_progress') },
      pending: { bg: 'bg-gray-100', text: 'text-gray-800', label: t('drugs.reports.pending') }
    };

    const config = statusConfig[status] || statusConfig.pending;
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-lg text-gray-600">Loading drug reports...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <div className="flex items-center gap-4">
            <Button
              variant="secondary"
              onClick={() => navigate('/admin/drugs')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {t('drugs.reports.back')}
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {t('drugs.reports.drug_reports')}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {t('drugs.reports.drug_reports_desc')}
              </p>
            </div>
          </div>
          
          <div className={`flex items-center gap-3 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Button
              variant="secondary"
              onClick={exportToCSV}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {t('drugs.reports.export_csv')}
            </Button>
            <Button
              variant="secondary"
              onClick={exportToPDFFile}
              className="flex items-center gap-2"
            >
              <FileText className="h-4 w-4" />
              {t('drugs.reports.export_pdf')}
            </Button>
            <Button
              variant="secondary"
              onClick={exportToPDF}
              className="flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              {t('drugs.reports.print')}
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('drugs.reports.total_purchases')}</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalPurchases}</div>
              <p className="text-xs text-muted-foreground">
                {summary.completedTransfers} {t('drugs.reports.completed_transfers')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('drugs.reports.total_investment')}</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(summary.totalInvestment)}</div>
              <p className="text-xs text-muted-foreground">
                {t('drugs.reports.purchase_costs')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('drugs.reports.current_value')}</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(summary.totalValue)}</div>
              <p className="text-xs text-muted-foreground">
                {t('drugs.reports.market_value')}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{t('drugs.reports.total_profit')}</CardTitle>
              <TrendingUp className={`h-4 w-4 ${summary.totalProfit >= 0 ? 'text-green-500' : 'text-red-500'}`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${summary.totalProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(summary.totalProfit)}
              </div>
              <p className="text-xs text-muted-foreground">
                {summary.profitMargin.toFixed(1)}% {t('drugs.reports.profit_margin')}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filter Controls */}
        <Card className="no-print">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                {t('drugs.reports.filter_by_status')}
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              {[
                { value: 'all', label: t('drugs.reports.all') },
                { value: 'completed', label: t('drugs.reports.completed') },
                { value: 'in_progress', label: t('drugs.reports.in_progress') },
                { value: 'pending', label: t('drugs.reports.pending') }
              ].map((option) => (
                <button
                  key={option.value}
                  onClick={() => setStatusFilter(option.value)}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    statusFilter === option.value
                      ? 'bg-[#FF6B00] text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Charts Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              {t('drugs.reports.process_analytics_dashboard')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Status Distribution Chart */}
              <div className="h-80">
                <h3 className="text-lg font-semibold mb-4">{t('drugs.reports.status_distribution')}</h3>
                {(() => {
                  const statusData = [
                    { name: t('drugs.reports.completed'), value: reportData.filter(item => item.status === 'completed').length, color: '#388E3C' },
                    { name: t('drugs.reports.in_progress'), value: reportData.filter(item => item.status === 'in_progress').length, color: '#FF6B00' },
                    { name: t('drugs.reports.pending'), value: reportData.filter(item => item.status === 'pending').length, color: '#FFC107' }
                  ].filter(item => item.value > 0); // Filter out zero values

                  const totalItems = reportData.length;

                  if (totalItems === 0 || statusData.length === 0) {
                    return (
                      <div className="flex items-center justify-center h-full">
                        <div className="text-center text-gray-500">
                          <PieChart className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                          <p className="text-lg font-medium mb-2">No Data Available</p>
                          <p className="text-sm">Add some drug purchases to see status distribution</p>
                        </div>
                      </div>
                    );
                  }

                  return (
                    <ResponsiveContainer width="100%" height="100%">
                      <RechartsPieChart>
                        <Pie
                          data={statusData}
                          cx="50%"
                          cy="50%"
                          outerRadius={100}
                          innerRadius={30}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percent, value }) =>
                            percent > 0.05 ? `${name}: ${value} (${(percent * 100).toFixed(1)}%)` : ''
                          }
                          labelLine={false}
                        >
                          {statusData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value, name) => [
                            `${value} items (${((value / totalItems) * 100).toFixed(1)}%)`,
                            name
                          ]}
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            border: 'none',
                            borderRadius: '8px',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
                          }}
                        />
                        <Legend
                          formatter={(value, entry) => `${value}: ${entry.payload.value}`}
                          wrapperStyle={{ paddingTop: '20px' }}
                        />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  );
                })()}
              </div>

              {/* Profit Analysis Chart */}
              <div className="h-80">
                <h3 className="text-lg font-semibold mb-4">{t('drugs.reports.profit_analysis')}</h3>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={filteredData.slice(0, 10)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="drugName" angle={-45} textAnchor="end" height={80} />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(value)} />
                    <Legend />
                    <Bar dataKey="totalAmount" fill="#FF6B00" name={t('drugs.reports.investment')} />
                    <Bar dataKey="currentValue" fill="#388E3C" name={t('drugs.reports.current_value')} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Analysis Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                {t('drugs.reports.detailed_process_analysis')}
              </div>
              <div className="text-sm text-gray-500">
                {filteredData.length} {t('drugs.reports.records')}
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('drugs.reports.drug_name')}</TableHead>
                    <TableHead>{t('drugs.reports.supplier')}</TableHead>
                    <TableHead>{t('drugs.reports.purchase_date')}</TableHead>
                    <TableHead>{t('drugs.reports.quantity')}</TableHead>
                    <TableHead>{t('drugs.reports.transferred')}</TableHead>
                    <TableHead>{t('drugs.reports.remaining')}</TableHead>
                    <TableHead>{t('drugs.reports.investment')}</TableHead>
                    <TableHead>{t('drugs.reports.current_value_table')}</TableHead>
                    <TableHead>{t('drugs.reports.profit_loss')}</TableHead>
                    <TableHead>{t('drugs.reports.status')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.drugName}</TableCell>
                      <TableCell>{item.supplier}</TableCell>
                      <TableCell>{formatDate(item.purchaseDate)}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell>
                        <span className="text-blue-600 font-medium">{item.transferred}</span>
                      </TableCell>
                      <TableCell>
                        <span className={`font-medium ${item.remaining > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {item.remaining}
                        </span>
                      </TableCell>
                      <TableCell>{formatCurrency(item.totalAmount)}</TableCell>
                      <TableCell>{formatCurrency(item.currentValue)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {getProfitIcon(item.profit)}
                          <div className="ml-2">
                            <div className={`font-medium ${item.profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {formatCurrency(item.profit)}
                            </div>
                            <div className={`text-xs ${item.profit >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                              {item.profitMargin.toFixed(1)}%
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(item.status)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DrugReportsPage;
