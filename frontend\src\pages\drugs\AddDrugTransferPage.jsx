import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, X } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import Button from '../../components/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import { useFarm } from '../../contexts/FarmContext';

const AddDrugTransferPage = () => {
  const navigate = useNavigate();
  const { language, t } = useLanguage();
  const { farms } = useFarm();

  const [formData, setFormData] = useState({
    purchaseId: '',
    farmId: '',
    transferDate: new Date().toISOString().split('T')[0],
    quantity: '',
    pricePerUnit: '',
    totalPrice: '',
    notes: '',
  });
  const [errors, setErrors] = useState({});
  const [submitError, setSubmitError] = useState(null);
  const [purchases, setPurchases] = useState([]);
  const [selectedPurchase, setSelectedPurchase] = useState(null);
  const [selectedFarm, setSelectedFarm] = useState(null);
  const [feedback, setFeedback] = useState({ type: '', message: '' });

  useEffect(() => {
    fetch('http://localhost:5432/api/v1/drug-purchases')
      .then(res => res.json())
      .then(json => setPurchases(json.data || []));
  }, []);





  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => {
      const newData = { ...prev, [name]: value };

      // Handle purchase selection
      if (name === 'purchaseId') {
        const purchase = purchases.find(p => p.id === parseInt(value));
        setSelectedPurchase(purchase);
        newData.quantity = ''; // reset quantity
        newData.pricePerUnit = purchase ? purchase.price_per_unit || '' : '';
        newData.totalPrice = '';
      }

      // Handle farm selection
      if (name === 'farmId') {
        const farm = farms?.find(f => f.id === parseInt(value));
        setSelectedFarm(farm);
      }

      // Auto-calculate prices when quantity or price per unit changes
      if (name === 'quantity' || name === 'pricePerUnit') {
        const quantity = parseFloat(name === 'quantity' ? value : newData.quantity) || 0;
        const pricePerUnit = parseFloat(name === 'pricePerUnit' ? value : newData.pricePerUnit) || 0;
        newData.totalPrice = (quantity * pricePerUnit).toFixed(2);
      }

      return newData;
    });

    // Clear errors for the field being changed
    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  // Calculate available quantity for the selected purchase
  const availableQuantity = selectedPurchase
    ? (selectedPurchase.remaining_quantity || (selectedPurchase.quantity - (selectedPurchase.transferred_quantity || 0)))
    : 0;

  const validateForm = () => {
    const newErrors = {};
    if (!formData.purchaseId) newErrors.purchaseId = 'Please select a drug purchase';
    if (!formData.farmId) newErrors.farmId = 'Please select a farm';
    if (!formData.quantity || parseInt(formData.quantity) < 1) newErrors.quantity = 'Quantity must be at least 1';
    if (parseInt(formData.quantity) > availableQuantity) newErrors.quantity = `Cannot transfer more than available (${availableQuantity})`;
    if (!formData.transferDate) newErrors.transferDate = 'Transfer date is required';
    if (!formData.pricePerUnit || parseFloat(formData.pricePerUnit) <= 0) newErrors.pricePerUnit = 'Price per unit is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitError(null);
    setFeedback({ type: '', message: '' });
    if (!validateForm()) return;
    try {
      const transferData = {
        purchaseId: parseInt(formData.purchaseId),
        farmId: parseInt(formData.farmId),
        quantity: parseInt(formData.quantity),
        transferDate: formData.transferDate,
        pricePerUnit: parseFloat(formData.pricePerUnit),
        totalPrice: parseFloat(formData.totalPrice),
        notes: formData.notes,
      };
      const response = await fetch('http://localhost:5432/api/v1/drug-transfers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(transferData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save transfer');
      }
      await response.json();
      // Navigate back with success message
      navigate('/admin/drugs/transfers?success=transfer_added');
    } catch (error) {
      setFeedback({ type: 'error', message: error.message || 'Failed to add transfer. Please try again.' });
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto space-y-6">
        <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => navigate('/admin/drugs/transfers')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('drugs.transfers.back_to_transfers')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{t('drugs.transfers.add.title')}</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{t('drugs.transfers.add.description')}</p>
          </div>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>{t('drugs.transfers.add.card_title')}</CardTitle>
          </CardHeader>
          <CardContent>
            {feedback.message && (
              <div
                className={`mb-6 p-4 rounded-lg ${
                  feedback.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                }`}
              >
                {feedback.message}
              </div>
            )}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('drugs.transfers.add.drug_purchase')} *</label>
                  <select
                    name="purchaseId"
                    value={formData.purchaseId}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  >
                    <option value="">{t('drugs.transfers.add.select_drug_purchase')}</option>
                    {purchases
                      .filter(purchase => {
                        const remaining = purchase.remaining_quantity || (purchase.quantity - (purchase.transferred_quantity || 0));
                        return remaining > 0;
                      })
                      .map((purchase) => {
                        const remaining = purchase.remaining_quantity || (purchase.quantity - (purchase.transferred_quantity || 0));
                        return (
                          <option
                            key={purchase.id}
                            value={purchase.id}
                          >
                            {purchase.drug_name} - {purchase.supplier} (Remaining: {remaining})
                          </option>
                        );
                      })}
                  </select>
                  {purchases.length === 0 && (
                    <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-sm text-yellow-700">
                        <strong>{t('drugs.transfers.add.no_purchases_available')}</strong> {t('drugs.transfers.add.need_create_purchases')}<br />
                        <a href="/admin/drugs/purchases/add" className="text-yellow-800 underline hover:text-yellow-900">{t('drugs.transfers.add.click_create_purchase')}</a>
                      </p>
                    </div>
                  )}
                  {purchases.length > 0 && purchases.filter(p => {
                    const remaining = p.remaining_quantity || (p.quantity - (p.transferred_quantity || 0));
                    return remaining > 0;
                  }).length === 0 && (
                    <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-md">
                      <p className="text-sm text-red-700">
                        <strong>{t('drugs.transfers.add.no_available_purchases')}</strong> {t('drugs.transfers.add.all_transferred')}<br />
                        <a href="/admin/drugs/purchases/add" className="text-red-800 underline hover:text-red-900">{t('drugs.transfers.add.create_new_purchase')}</a> {t('drugs.transfers.add.to_continue_transferring')}
                      </p>
                    </div>
                  )}
                  {selectedPurchase && (
                    <div className={`mt-2 p-3 rounded-md ${availableQuantity > 0 ? 'bg-blue-50' : 'bg-red-50'}`}>
                      <div className={`text-sm ${availableQuantity > 0 ? 'text-blue-700' : 'text-red-700'}`}>
                        <div><strong>{t('drugs.transfers.add.drug')}:</strong> {selectedPurchase.drug_name}</div>
                        <div><strong>{t('drugs.transfers.add.supplier')}:</strong> {selectedPurchase.supplier}</div>
                        <div><strong>{t('drugs.transfers.add.total_quantity')}:</strong> {selectedPurchase.quantity}</div>
                        <div><strong>{t('drugs.transfers.add.transferred')}:</strong> {selectedPurchase.transferred_quantity || 0}</div>
                        <div><strong>{t('drugs.transfers.add.remaining')}:</strong>
                          <span className={`ml-1 font-bold ${availableQuantity > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {availableQuantity}
                          </span>
                          {availableQuantity === 0 && (
                            <span className="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                              {t('drugs.transfers.add.completed')}
                            </span>
                          )}
                        </div>
                        <div><strong>{t('drugs.transfers.add.purchase_date')}:</strong> {selectedPurchase.purchase_date ? new Date(selectedPurchase.purchase_date).toLocaleDateString() : ''}</div>
                      </div>
                    </div>
                  )}
                  {errors.purchaseId && <p className="text-red-500 text-sm mt-1">{errors.purchaseId}</p>}
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('drugs.transfers.add.farm')} *</label>
                  <select
                    name="farmId"
                    value={formData.farmId}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  >
                    <option value="">{t('drugs.transfers.add.select_farm')}</option>
                    {farms && farms.length > 0 ? farms.map((farm) => (
                      <option key={farm.id} value={farm.id}>
                        {farm.name} - {farm.owner}
                      </option>
                    )) : (
                      <option value="" disabled>{t('drugs.transfers.add.no_farms_available')}</option>
                    )}
                  </select>
                  {(!farms || farms.length === 0) && (
                    <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-sm text-yellow-700">
                        <strong>{t('drugs.transfers.add.no_farms_available')}</strong> {t('drugs.transfers.add.need_create_farms')}<br />
                        <a href="/admin/farms/add" className="text-yellow-800 underline hover:text-yellow-900">{t('drugs.transfers.add.click_create_farm')}</a>
                      </p>
                    </div>
                  )}
                  {selectedFarm && (
                    <div className="mt-2 p-3 bg-green-50 rounded-md">
                      <div className="text-sm text-green-700">
                        <div><strong>{t('drugs.transfers.add.farm')}:</strong> {selectedFarm.name}</div>
                        <div><strong>{t('drugs.transfers.add.owner')}:</strong> {selectedFarm.owner}</div>
                        <div><strong>{t('drugs.transfers.add.email')}:</strong> {selectedFarm.email}</div>
                        <div><strong>{t('drugs.transfers.add.phone')}:</strong> {selectedFarm.phone}</div>
                      </div>
                    </div>
                  )}
                  {errors.farmId && <p className="text-red-500 text-sm mt-1">{errors.farmId}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('drugs.transfers.add.transfer_date')} *</label>
                  <input
                    type="date"
                    name="transferDate"
                    value={formData.transferDate}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  />
                  {errors.transferDate && <p className="text-red-500 text-sm mt-1">{errors.transferDate}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('drugs.transfers.add.quantity')} *</label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleChange}
                    required
                    min="1"
                    max={availableQuantity}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    placeholder={availableQuantity ? `${t('drugs.transfers.add.max')}: ${availableQuantity}` : t('drugs.transfers.add.enter_quantity')}
                    disabled={!selectedPurchase}
                  />
                  {selectedPurchase && (
                    <small className="text-gray-500">
                      {t('drugs.transfers.add.maximum_available')}: {availableQuantity}
                    </small>
                  )}
                  {errors.quantity && <p className="text-red-500 text-sm mt-1">{errors.quantity}</p>}
                </div>

                {/* Price Per Unit */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('drugs.transfers.add.price_per_unit')} *
                  </label>
                  <input
                    type="number"
                    name="pricePerUnit"
                    value={formData.pricePerUnit}
                    onChange={handleChange}
                    required
                    min="0"
                    step="0.01"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    placeholder={t('drugs.transfers.add.enter_price_per_unit')}
                    disabled={!selectedPurchase}
                  />
                  {selectedPurchase && (
                    <small className="text-gray-500">
                      {t('drugs.transfers.add.purchase_price')}: {selectedPurchase.price_per_unit || 0} AFN {t('drugs.transfers.add.per_unit')}
                    </small>
                  )}
                  {errors.pricePerUnit && <p className="text-red-500 text-sm mt-1">{errors.pricePerUnit}</p>}
                </div>

                {/* Total Price (Auto-calculated) */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('drugs.transfers.add.total_price')}
                  </label>
                  <input
                    type="number"
                    name="totalPrice"
                    value={formData.totalPrice}
                    readOnly
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 dark:bg-gray-600 dark:text-white dark:border-gray-600"
                    placeholder={t('drugs.transfers.add.auto_calculated')}
                  />
                  <small className="text-gray-500">
                    {t('drugs.transfers.add.auto_calculated_formula')}
                  </small>
                </div>

                {/* Profit/Loss Information */}
                {selectedPurchase && formData.pricePerUnit && formData.quantity && (
                  <div className="md:col-span-2">
                    {(() => {
                      const purchasePrice = parseFloat(selectedPurchase.price_per_unit) || 0;
                      const transferPrice = parseFloat(formData.pricePerUnit) || 0;
                      const profitPerUnit = transferPrice - purchasePrice;
                      const totalProfit = profitPerUnit * (parseInt(formData.quantity) || 0);
                      const isProfit = profitPerUnit > 0;
                      const isLoss = profitPerUnit < 0;

                      return (
                        <div className={`border rounded-lg p-4 ${
                          isProfit ? 'bg-green-50 border-green-200' :
                          isLoss ? 'bg-red-50 border-red-200' :
                          'bg-gray-50 border-gray-200'
                        }`}>
                          <h3 className={`text-lg font-semibold mb-3 ${
                            isProfit ? 'text-green-800' :
                            isLoss ? 'text-red-800' :
                            'text-gray-800'
                          }`}>
                            💰 {isProfit ? 'Profit' : isLoss ? 'Loss' : 'Break Even'} Analysis
                          </h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className={`block text-sm font-medium mb-2 ${
                                isProfit ? 'text-green-700' :
                                isLoss ? 'text-red-700' :
                                'text-gray-700'
                              }`}>
                                {isProfit ? 'Profit' : isLoss ? 'Loss' : 'Difference'} Per Unit
                              </label>
                              <div className={`text-2xl font-bold ${
                                isProfit ? 'text-green-600' :
                                isLoss ? 'text-red-600' :
                                'text-gray-600'
                              }`}>
                                AFN {Math.abs(profitPerUnit).toFixed(2)}
                              </div>
                              <small className={`${
                                isProfit ? 'text-green-600' :
                                isLoss ? 'text-red-600' :
                                'text-gray-600'
                              }`}>
                                Transfer price - Purchase price
                              </small>
                            </div>
                            <div>
                              <label className={`block text-sm font-medium mb-2 ${
                                isProfit ? 'text-green-700' :
                                isLoss ? 'text-red-700' :
                                'text-gray-700'
                              }`}>
                                Total {isProfit ? 'Profit' : isLoss ? 'Loss' : 'Amount'}
                              </label>
                              <div className={`text-2xl font-bold ${
                                isProfit ? 'text-green-600' :
                                isLoss ? 'text-red-600' :
                                'text-gray-600'
                              }`}>
                                AFN {Math.abs(totalProfit).toFixed(2)}
                              </div>
                              <small className={`${
                                isProfit ? 'text-green-600' :
                                isLoss ? 'text-red-600' :
                                'text-gray-600'
                              }`}>
                                {formData.quantity || 0} units × AFN {Math.abs(profitPerUnit).toFixed(2)}
                              </small>
                            </div>
                          </div>
                          <div className={`mt-3 p-3 rounded-md ${
                            isProfit ? 'bg-green-100' :
                            isLoss ? 'bg-red-100' :
                            'bg-gray-100'
                          }`}>
                            <p className={`text-sm ${
                              isProfit ? 'text-green-800' :
                              isLoss ? 'text-red-800' :
                              'text-gray-800'
                            }`}>
                              <strong>Calculation:</strong> Transfer price (AFN {transferPrice.toFixed(2)}) - Purchase price (AFN {purchasePrice.toFixed(2)}) = AFN {profitPerUnit.toFixed(2)} {isProfit ? 'profit' : isLoss ? 'loss' : 'break even'} per unit
                            </p>
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                )}


                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{t('drugs.transfers.add.notes')}</label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder={t('drugs.transfers.add.enter_notes')}
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>
                </div>
              <div className={`flex justify-end gap-2 pt-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => navigate('/admin/drugs/transfers')}
                >
                  {t('drugs.transfers.cancel')}
                </Button>
                <Button
                  type="submit"
                  disabled={availableQuantity <= 0}
                  className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''} ${
                    availableQuantity <= 0 ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  <Save className="h-4 w-4" />
                  {availableQuantity <= 0 ? t('drugs.transfers.add.transfer_completed') : t('drugs.transfers.add.save_transfer')}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AddDrugTransferPage;
