/* eslint-disable react/jsx-key */
import { useState, useEffect } from 'react';
import { Card, List, Button, Space, Tag, Badge, Empty, Popconfirm, Modal, Descriptions } from 'antd';
import { CheckOutlined, DeleteOutlined, ReloadOutlined, EyeOutlined, MailOutlined, PhoneOutlined, UserOutlined, CalendarOutlined } from '@ant-design/icons';
import { useLanguage } from '../../contexts/LanguageContext';
import { useNotifications } from '../../contexts/NotificationContext';
import axios from 'axios';

const NotificationsPage = () => {
  const { language, translations } = useLanguage();
  const {
    notifications,
    notificationSettings,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    archiveNotification,
    updateNotificationSettings,
    fetchNotifications,
    fetchUnreadCount,
  } = useNotifications();

  const [activeTab, setActiveTab] = useState('contacts');

  const [contactDetails, setContactDetails] = useState(null);
  const [showContactModal, setShowContactModal] = useState(false);
  const [allContacts, setAllContacts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [readContacts, setReadContacts] = useState(() => {
    // Load from localStorage as fallback
    try {
      const saved = localStorage.getItem('readContacts');
      return saved ? new Set(JSON.parse(saved)) : new Set();
    } catch (error) {
      return new Set();
    }
  });

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    // Support nested keys like 'notifications.title'
    const keys = key.split('.');
    let value = translations[language];
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return key;
      }
    }
    return typeof value === 'string' ? value : key;
  };

  // Fetch all contact submissions
  const fetchAllContacts = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:5432/api/v1/contact-us', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        // Sort contacts by newest first (latest submissions at top)
        const sortedContacts = (response.data.data || []).sort((a, b) => {
          // Try to sort by created_at first, then fall back to C_Id (higher ID = newer)
          const dateA = new Date(a.created_at || a.C_Id);
          const dateB = new Date(b.created_at || b.C_Id);
          return dateB - dateA; // Newest first
        });

        setAllContacts(sortedContacts);
        console.log('✅ Contacts loaded and sorted (newest first):', sortedContacts.length, 'contacts');
        console.log('📋 First 3 contacts:', sortedContacts.slice(0, 3).map(c => ({ id: c.C_Id, name: c.user_name, title: c.C_Title })));
      }
    } catch (error) {
      console.error('Error fetching contacts:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch contact details
  const fetchContactDetails = async (contactId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`http://localhost:5432/api/v1/contact-us/${contactId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setContactDetails(response.data.data);
        setShowContactModal(true);
      }
    } catch (error) {
      console.error('Error fetching contact details:', error);
    }
  };

  // Delete contact
  const deleteContact = async (contactId) => {
    try {
      const token = localStorage.getItem('token');
      await axios.delete(`http://localhost:5432/api/v1/contact-us/${contactId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Refresh contacts list and notifications
      fetchAllContacts();
      fetchNotifications();
    } catch (error) {
      console.error('Error deleting contact:', error);
    }
  };

  useEffect(() => {
    fetchAllContacts();
    fetchReadStatus();
    // Debug: Log notifications to see what we have
    console.log('📋 Current notifications:', notifications);
    console.log('📋 Contact notifications:', notifications.filter(n => n.category === 'contacts'));
  }, [notifications]);

  // Also fetch read status when contacts change
  useEffect(() => {
    if (allContacts.length > 0) {
      fetchReadStatus();
    }
  }, [allContacts]);

  // Save to localStorage whenever readContacts changes (as backup)
  useEffect(() => {
    try {
      localStorage.setItem('readContacts', JSON.stringify([...readContacts]));
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }, [readContacts]);

  // Fetch read status from database
  const fetchReadStatus = async () => {
    try {
      console.log('🔄 Fetching read status from database...');
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('❌ No auth token found');
        return;
      }

      console.log('📡 Making API call to get read status...');
      const response = await axios.get('http://localhost:5432/api/v1/contact-read-status', {
        headers: { Authorization: `Bearer ${token}` }
      });

      console.log('📡 Read status API response:', response.data);

      if (response.data.success) {
        const readContactIds = response.data.data || [];
        setReadContacts(new Set(readContactIds));
        console.log('✅ Read status loaded from database:', readContactIds);
        console.log('✅ Read contacts set updated:', [...new Set(readContactIds)]);
      } else {
        console.log('❌ API response not successful:', response.data);
      }
    } catch (error) {
      console.error('❌ Error fetching read status:', error);
      console.error('❌ Error details:', error.response?.data || error.message);
    }
  };



  // Hybrid approach: Update UI immediately + try to save to database
  const markContactAsRead = async (contactId) => {
    console.log('🔄 Marking contact as read:', contactId);

    // Update UI immediately (this will also save to localStorage via useEffect)
    setReadContacts(prev => {
      const newSet = new Set([...prev, contactId]);
      console.log('✅ Contact marked as read:', contactId);
      return newSet;
    });

    // Try to save to database in background (don't wait for it)
    try {
      const token = localStorage.getItem('token');
      if (token) {
        axios.post(`http://localhost:5432/api/v1/contact-read-status/${contactId}/read`, {}, {
          headers: { Authorization: `Bearer ${token}` }
        }).catch(error => {
          console.log('Database save failed, but localStorage backup is working:', error.message);
        });
      }
    } catch (error) {
      console.log('Database save failed, but localStorage backup is working:', error.message);
    }
  };

  const markAllContactsAsRead = async () => {
    console.log('🔄 Marking all contacts as read...');
    const allContactIds = allContacts.map(contact => contact.C_Id);

    // Update UI immediately (this will also save to localStorage via useEffect)
    setReadContacts(new Set(allContactIds));
    console.log('✅ All contacts marked as read');

    // Try to save to database in background
    try {
      const token = localStorage.getItem('token');
      if (token) {
        axios.post('http://localhost:5432/api/v1/contact-read-status/mark-all-read', {}, {
          headers: { Authorization: `Bearer ${token}` }
        }).catch(error => {
          console.log('Database save failed, but localStorage backup is working:', error.message);
        });
      }
    } catch (error) {
      console.log('Database save failed, but localStorage backup is working:', error.message);
    }
  };

  const clearAllReadStatus = () => {
    console.log('🔄 Clearing all read status...');

    // Update UI immediately (this will also clear localStorage via useEffect)
    setReadContacts(new Set());
    console.log('✅ All read status cleared');

    // Try to clear from database in background
    try {
      const token = localStorage.getItem('token');
      if (token) {
        axios.delete('http://localhost:5432/api/v1/contact-read-status/clear-all', {
          headers: { Authorization: `Bearer ${token}` }
        }).catch(error => {
          console.log('Database clear failed, but localStorage backup is working:', error.message);
        });
      }
    } catch (error) {
      console.log('Database clear failed, but localStorage backup is working:', error.message);
    }
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'low_stock':
        return <Tag color="red">{t('low_stock')}</Tag>;
      case 'expiry_alert':
        return <Tag color="orange">{t('expiry_alert')}</Tag>;
      case 'new_order':
        return <Tag color="blue">{t('new_order')}</Tag>;
      case 'new_user':
        return <Tag color="green">{t('new_user')}</Tag>;
      case 'new_contact':
        return <Tag color="cyan">Contact Form</Tag>;
      case 'report_generated':
        return <Tag color="purple">{t('report_generated')}</Tag>;
      default:
        return <BellOutlined />;
    }
  };

  // Only show contact notifications
  const filteredNotifications = notifications.filter((notification) => {
    return notification.category === 'contacts' && !notification.archived;
  });



  const contactNotificationsCount = notifications.filter(n => n.category === 'contacts' && !n.archived).length;

  // Only show Contacts tab
  const tabItems = [
    {
      key: 'contacts',
      label: (
        <Space>
          <MailOutlined />
          {t('notifications.contact_submissions')}
          {contactNotificationsCount > 0 && (
            <Badge count={contactNotificationsCount} size="small" />
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="notifications-page">
      <Card
        title={
          <Space>
            <MailOutlined />
            {t('notifications.contact_notifications')}
            <Badge count={contactNotificationsCount} />
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                // Refresh both notifications and contacts
                fetchNotifications();
                fetchUnreadCount();
                fetchAllContacts();
                fetchReadStatus();
              }}
            >
              {t('notifications.refresh')}
            </Button>



            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={async () => {
                try {
                  // Mark all contact notifications as read
                  const contactNotifications = notifications.filter(n => n.category === 'contacts' && !n.read);

                  for (const notification of contactNotifications) {
                    await markAsRead(notification.id);
                  }

                  // Mark all contacts as read (remove green dots)
                  markAllContactsAsRead();

                  // Refresh to update the display
                  setTimeout(() => {
                    fetchNotifications();
                    fetchUnreadCount();
                  }, 500);
                } catch (error) {
                  console.error('Error marking all as read:', error);
                }
              }}
              disabled={allContacts.length === 0 || allContacts.every(contact => readContacts.has(contact.C_Id))}
            >
              {t('notifications.mark_all_read')}
            </Button>

            <Popconfirm
              title={t('notifications.confirm_clear_all')}
              onConfirm={async () => {
                try {
                  // Clear contact notifications
                  const contactNotifications = notifications.filter(n => n.category === 'contacts');
                  for (const notification of contactNotifications) {
                    await clearNotification(notification.id);
                  }

                  // Also delete all contact submissions
                  for (const contact of allContacts) {
                    await deleteContact(contact.C_Id);
                  }

                  // Reset read contacts
                  clearAllReadStatus();

                  // Refresh to update the display
                  setTimeout(() => {
                    fetchNotifications();
                    fetchUnreadCount();
                    fetchAllContacts();
                  }, 500);
                } catch (error) {
                  console.error('Error clearing all contacts:', error);
                }
              }}
              okText={t('common.yes')}
              cancelText={t('common.no')}
            >
              <Button type="primary" danger icon={<DeleteOutlined />}>
                {t('notifications.clear_all_contacts')}
              </Button>
            </Popconfirm>
          </Space>
        }
      >




        {/* Show notifications if they exist, otherwise show contact submissions directly */}
        {filteredNotifications.length === 0 ? (
          <div>
            {/* If no notifications but we have contacts, show them directly */}
            {allContacts.length > 0 ? (
              <div>
                <h3 className="text-lg font-semibold mb-4">
                  All Contact Submissions
                  <span className="text-sm text-gray-500 font-normal ml-2">(Latest First)</span>
                </h3>
                <List
                  itemLayout="horizontal"
                  dataSource={allContacts}
                  renderItem={(contact) => (
                    <List.Item
                      actions={[
                        <Button
                          type="link"
                          icon={<EyeOutlined />}
                          onClick={() => {
                            // Mark this contact as read when viewing details
                            markContactAsRead(contact.C_Id);
                            setContactDetails(contact);
                            setShowContactModal(true);
                          }}
                        >
                          {t('notifications.view_details')}
                        </Button>,
                        <Popconfirm
                          title={t('notifications.confirm_delete_contact')}
                          onConfirm={() => deleteContact(contact.C_Id)}
                          okText={t('common.yes')}
                          cancelText={t('common.no')}
                        >
                          <Button type="link" danger icon={<DeleteOutlined />}>
                            {t('notifications.delete')}
                          </Button>
                        </Popconfirm>,
                      ]}
                    >
                      <List.Item.Meta
                        avatar={<UserOutlined style={{ fontSize: 20 }} />}
                        title={
                          <Space>
                            {!readContacts.has(contact.C_Id) && (
                              <span style={{ fontSize: '12px' }}>🟢</span>
                            )}
                            <span>{contact.user_name}</span>
                            <span className="text-gray-500">-</span>
                            <span>{contact.C_Title}</span>
                          </Space>
                        }
                        description={
                          <div>
                            <Space>
                              <MailOutlined />
                              {contact.user_email}
                              {contact.user_phone && (
                                <>
                                  <PhoneOutlined />
                                  {contact.user_phone}
                                </>
                              )}
                            </Space>
                            <div className="mt-1 text-xs text-gray-400">
                              <CalendarOutlined /> {t('notifications.submitted')}: {new Date(contact.created_at || contact.C_Id).toLocaleString()}
                            </div>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </div>
            ) : (
              <Empty
                image={<MailOutlined style={{ fontSize: 48, color: '#1890ff' }} />}
                description={t('notifications.no_contact_submissions')}
                className="py-8"
              />
            )}
          </div>
        ) : (
          <List
            itemLayout="horizontal"
            dataSource={filteredNotifications}
            renderItem={(notification) => (
              <List.Item
                actions={[
                  // Contact-specific view details button
                  notification.type === 'new_contact' && notification.data.contactId && (
                    <Button
                      type="link"
                      icon={<EyeOutlined />}
                      onClick={() => fetchContactDetails(notification.data.contactId)}
                    >
                      {t('notifications.view_details')}
                    </Button>
                  ),
                  !notification.read && (
                    <Button type="link" onClick={() => markAsRead(notification.id)}>
                      {t('notifications.mark_as_read')}
                    </Button>
                  ),
                  <Button type="link" danger onClick={() => clearNotification(notification.id)}>
                    {t('notifications.delete')}
                  </Button>,
                  <Button type="link" onClick={() => archiveNotification(notification.id)}>
                    {t('common.archive')}
                  </Button>,
                ].filter(Boolean)}
              >
                <List.Item.Meta
                  avatar={getNotificationIcon(notification.type)}
                  title={
                    <Space>
                      <span>{notification.title || notification.message}</span>
                      {!notification.read && <div className="w-2 h-2 bg-blue-500 rounded-full"></div>}
                    </Space>
                  }
                  description={
                    <div>
                      <div>{notification.message}</div>
                      {notification.type === 'new_contact' && notification.data && (
                        <div className="mt-2 text-sm text-gray-500">
                          <Space>
                            <UserOutlined />
                            {notification.data.userName}
                            <MailOutlined />
                            {notification.data.userEmail}
                            {notification.data.phone && (
                              <>
                                <PhoneOutlined />
                                {notification.data.phone}
                              </>
                            )}
                          </Space>
                        </div>
                      )}
                      <div className="mt-1 text-xs text-gray-400">
                        <CalendarOutlined /> {new Date(notification.timestamp).toLocaleString()}
                      </div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </Card>

      {/* Contact Details Modal */}
      <Modal
        title={t('notifications.contact_details')}
        open={showContactModal}
        onCancel={() => {
          setShowContactModal(false);
          setContactDetails(null);
        }}
        footer={[
          <Button key="close" onClick={() => setShowContactModal(false)}>
            {t('notifications.close')}
          </Button>,
          contactDetails && (
            <Popconfirm
              key="delete"
              title={t('notifications.confirm_delete_contact')}
              onConfirm={() => {
                deleteContact(contactDetails.C_Id);
                setShowContactModal(false);
                setContactDetails(null);
              }}
              okText={t('common.yes')}
              cancelText={t('common.no')}
            >
              <Button type="primary" danger icon={<DeleteOutlined />}>
                {t('notifications.delete_contact')}
              </Button>
            </Popconfirm>
          ),
        ]}
        width={600}
      >
        {contactDetails && (
          <Descriptions bordered column={1}>
            <Descriptions.Item label={t('notifications.name')}>
              <Space>
                <UserOutlined />
                {contactDetails.user_name}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label={t('notifications.email')}>
              <Space>
                <MailOutlined />
                {contactDetails.user_email}
              </Space>
            </Descriptions.Item>
            {contactDetails.user_phone && (
              <Descriptions.Item label={t('notifications.phone')}>
                <Space>
                  <PhoneOutlined />
                  {contactDetails.user_phone}
                </Space>
              </Descriptions.Item>
            )}
            <Descriptions.Item label={t('notifications.subject')}>
              {contactDetails.C_Title}
            </Descriptions.Item>
            <Descriptions.Item label={t('notifications.message')}>
              <div className="whitespace-pre-wrap">
                {contactDetails.C_Body}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label={t('notifications.submitted')}>
              <Space>
                <CalendarOutlined />
                {new Date(contactDetails.created_at || new Date()).toLocaleString()}
              </Space>
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default NotificationsPage;
